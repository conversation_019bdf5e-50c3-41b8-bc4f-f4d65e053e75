from flask import Blueprint, request, jsonify, session, make_response
from app.config import get_db_connection
from app.services.audit_log import AuditLogService
import json
import datetime
import random
import os # for file uploads if needed later
from werkzeug.utils import secure_filename
from app.services.sample_notification_service import SampleNotificationService

teacher_bp = Blueprint('teacher', __name__)

# 创建邮件通知服务实例
notification_service = SampleNotificationService()

@teacher_bp.route('/get_addresses', methods=['GET'])
def get_addresses():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    teacher_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询总地址数量
            cursor.execute("SELECT COUNT(*) AS count FROM shipping_addresses WHERE teacher_id = %s", (teacher_id,))
            total_count = cursor.fetchone()['count']

            # 查询当前页的地址信息
            sql = """
                SELECT address_id, name, phone_number, province, city, district, detailed_address
                FROM shipping_addresses
                WHERE teacher_id = %s
                LIMIT %s OFFSET %s
            """
            cursor.execute(sql, (teacher_id, limit, offset))
            addresses = cursor.fetchall()

            # 为每个地址组装完整地址用于显示
            for address in addresses:
                address['full_address'] = f"{address['province']}{address['city']}{address['district']}{address['detailed_address']}"

            # 返回符合 layui 表格要求的数据格式
            return jsonify({
                "code": 0,
                "msg": "",
                "count": total_count,
                "data": addresses
            })
    except Exception as e:
        return jsonify({"code": 1, "message": "获取地址失败"})
    finally:
        connection.close()



@teacher_bp.route('/add_address', methods=['POST'])
def add_address():
    """添加收货地址"""
    try:
        # 检查登录状态
        if 'user_id' not in session or session['role'] != 'teacher':
            return jsonify({"code": 1, "message": "请登录后再进行操作"}), 401

        # 获取请求数据
        name = request.form.get('name')
        phone_number = request.form.get('phone_number')
        province = request.form.get('province')
        city = request.form.get('city')
        district = request.form.get('district')
        detailed_address = request.form.get('detailed_address')
        postal_code = request.form.get('postal_code', '')  # 邮政编码（可选）

        # 参数验证
        if not name:
            return jsonify({"code": 1, "message": "请提供收件人姓名"}), 400
        
        if not phone_number:
            return jsonify({"code": 1, "message": "请提供联系电话"}), 400
        
        if not province:
            return jsonify({"code": 1, "message": "请选择省份"}), 400
            
        if not city:
            return jsonify({"code": 1, "message": "请选择城市"}), 400
            
        if not district:
            return jsonify({"code": 1, "message": "请选择区县"}), 400
        
        if not detailed_address:
            return jsonify({"code": 1, "message": "请提供详细地址"}), 400

        # 获取用户ID
        teacher_id = session.get('user_id')
        
        # 执行数据库操作
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 插入地址数据
                sql = """
                INSERT INTO shipping_addresses (teacher_id, name, phone_number, province, city, district, detailed_address)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (teacher_id, name, phone_number, province, city, district, detailed_address))
                connection.commit()
                
                # 获取新插入的地址ID
                address_id = cursor.lastrowid
                
                return jsonify({
                    "code": 0,
                    "message": "地址添加成功",
                    "data": {
                        "address_id": address_id,
                        "name": name,
                        "phone_number": phone_number,
                        "province": province,
                        "city": city,
                        "district": district,
                        "detailed_address": detailed_address,
                        "full_address": f"{province}{city}{district}{detailed_address}"
                    }
                })
        finally:
            connection.close()
    
    except Exception as e:
        # print(f"添加地址时发生错误: {str(e)}")
        return jsonify({"code": 1, "message": f"添加地址失败: {str(e)}"}), 500

@teacher_bp.route('/edit_address', methods=['POST'])
def edit_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    address_id = request.form.get('address_id')
    name = request.form.get('name')
    phone_number = request.form.get('phone_number')
    province = request.form.get('province')
    city = request.form.get('city')
    district = request.form.get('district')
    detailed_address = request.form.get('detailed_address')
    postal_code = request.form.get('postal_code', '')  # 邮政编码（可选）

    if not address_id or not name or not phone_number or not province or not city or not district or not detailed_address:
        return jsonify({"code": 1, "message": "所有字段均为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                UPDATE shipping_addresses
                SET name = %s, phone_number = %s, province = %s, city = %s, district = %s, detailed_address = %s
                WHERE address_id = %s AND teacher_id = %s
            """
            cursor.execute(sql, (name, phone_number, province, city, district, detailed_address, address_id, session['user_id']))
            connection.commit()
            
            if cursor.rowcount > 0:
                return jsonify({"code": 0, "message": "地址修改成功"})
            else:
                return jsonify({"code": 1, "message": "地址不存在或无权限修改"})
    except Exception as e:
        return jsonify({"code": 1, "message": "修改地址失败"})
    finally:
        connection.close()



# PC端接口 - 批量申请样书
@teacher_bp.route('/request_samples', methods=['POST'])
def request_samples():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        address_id = data.get('address_id')
        books_data = data.get('books_data')

        if not address_id:
            return jsonify({"code": 1, "message": "请选择收货地址"})
        if not books_data or not isinstance(books_data, list) or len(books_data) == 0:
            return jsonify({"code": 1, "message": "请选择样书并填写相关信息"})

        # 生成订单编号：T+时间戳+用户ID后四位+4位随机数
        current_time = datetime.datetime.now()
        timestamp = current_time.strftime('%Y%m%d%H%M%S')
        user_id_suffix = str(session['user_id'])[-4:].zfill(4)
        random_num = ''.join([str(random.randint(0, 9)) for _ in range(4)])
        order_number = f"T{timestamp}{user_id_suffix}{random_num}"
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 验证地址是否存在且属于当前教师
                sql_check_address = "SELECT address_id FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
                cursor.execute(sql_check_address, (address_id, session['user_id']))
                if not cursor.fetchone():
                    return jsonify({"code": 1, "message": "收货地址无效或不属于您"})

                success_count = 0
                fail_count = 0
                duplicate_count = 0
                failed_books_info = []
                submitted_request_ids = []  # 记录成功提交的申请ID

                for book_item in books_data:
                    book_id = book_item.get('book_id')
                    quantity = book_item.get('quantity', 1)
                    course_id = book_item.get('course_id')
                    purpose = book_item.get('purpose', '教材')  # 使用每本书独立的用途
                    item_remark = book_item.get('item_remark', '')

                    if not book_id or not course_id:
                        fail_count += 1
                        failed_books_info.append(f"书籍ID {book_id or '未知'} 信息不完整")
                        continue
                    
                    try:
                        quantity = int(quantity)
                        if quantity <= 0:
                            fail_count += 1
                            failed_books_info.append(f"书籍ID {book_id} 数量无效")
                            continue
                    except ValueError:
                        fail_count += 1
                        failed_books_info.append(f"书籍ID {book_id} 数量格式错误")
                        continue

                    # 验证课程是否存在且属于当前用户
                    sql_check_course = "SELECT id FROM teacher_courses WHERE id = %s AND teacher_id = %s"
                    cursor.execute(sql_check_course, (course_id, session['user_id']))
                    if not cursor.fetchone():
                        fail_count += 1
                        failed_books_info.append(f"书籍ID {book_id} 的课程无效或不属于您")
                        continue
                    
                    # 检查样书是否存在
                    sql_check_book = "SELECT id FROM sample_books WHERE id = %s"
                    cursor.execute(sql_check_book, (book_id,))
                    if not cursor.fetchone():
                        fail_count += 1
                        failed_books_info.append(f"书籍ID {book_id} 不存在")
                        continue
                    
                    # 检查是否已经申请过该样书 (待审核或已批准状态)
                    sql_check_existing = """
                        SELECT request_id FROM sample_requests 
                        WHERE textbook_id = %s AND teacher_id = %s 
                        AND status IN ('pending', 'approved')
                    """
                    cursor.execute(sql_check_existing, (book_id, session['user_id']))
                    if cursor.fetchone():
                        duplicate_count += 1
                        continue
                    
                    # 插入申请记录，每本书使用独立的用途
                    sql_insert_request = """
                        INSERT INTO sample_requests 
                        (order_number, teacher_id, textbook_id, address_id, status, request_date, 
                         course_id, request_reason, quantity, purpose) 
                        VALUES (%s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s)
                    """
                    cursor.execute(sql_insert_request, (
                        order_number, session['user_id'], book_id, address_id, 'pending', 
                        course_id, item_remark, quantity, purpose
                    ))
                    submitted_request_ids.append(cursor.lastrowid)
                    success_count += 1
                
                connection.commit()
                
                # 发送邮件通知出版社用户
                if submitted_request_ids:
                    try:
                        print(f"[DEBUG] 准备发送样书申请邮件通知:")
                        print(f"  - 教师ID: {session['user_id']}")
                        print(f"  - 申请ID列表: {submitted_request_ids}")
                        
                        result = notification_service.notify_publisher_sample_requests(
                            session['user_id'], submitted_request_ids
                        )
                        
                        print(f"[DEBUG] 邮件发送结果: {result}")
                        
                        if result:
                            print(f"[DEBUG] 样书申请邮件通知发送成功")
                        else:
                            print(f"[DEBUG] 样书申请邮件通知发送失败")
                            
                    except Exception as e:
                        print(f"[ERROR] 发送样书申请邮件通知时发生异常:")
                        print(f"  - 异常类型: {type(e).__name__}")
                        print(f"  - 异常信息: {str(e)}")
                        import traceback
                        print(f"  - 详细堆栈: {traceback.format_exc()}")
                        # 邮件发送失败不影响业务流程
                else:
                    print(f"[DEBUG] 没有成功提交的申请ID，跳过邮件发送")
                
                message = f"成功申请 {success_count} 本样书。"
                if fail_count > 0:
                    message += f" {fail_count} 本申请失败。"
                if duplicate_count > 0:
                    message += f" {duplicate_count} 本为重复申请，已跳过。"
                if failed_books_info:
                    message += f" 失败详情: {'; '.join(failed_books_info)}."

                response_data = {
                    "code": 0, 
                    "message": message,
                    "success_count": success_count,
                    "fail_count": fail_count,
                    "duplicate_count": duplicate_count,
                }
                
                if success_count > 0:
                    response_data["order_number"] = order_number
                    response_data["message"] += " 已发送邮件通知相关出版社。"

                # 记录样书申请日志
                result = AuditLogService.Result.SUCCESS if fail_count == 0 else (
                    AuditLogService.Result.PARTIAL if success_count > 0 else AuditLogService.Result.FAILURE
                )

                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
                    result=result,
                    description=f"教师申请样书，订单号：{order_number}",
                    target_type='sample_request',
                    target_id=order_number,
                    details={
                        'order_number': order_number,
                        'total_books': len(books_data),
                        'success_count': success_count,
                        'fail_count': fail_count,
                        'duplicate_count': duplicate_count,
                        'address_id': address_id
                    }
                )

                return jsonify(response_data)

        except Exception as e:
            if connection: connection.rollback()

            # 记录样书申请失败日志
            AuditLogService.log_action(
                action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
                result=AuditLogService.Result.FAILURE,
                description="样书申请失败",
                details={
                    'error_reason': str(e),
                    'books_count': len(books_data) if 'books_data' in locals() else 0,
                    'address_id': address_id if 'address_id' in locals() else None
                }
            )

            return jsonify({"code": 1, "message": f"申请样书时发生错误: {str(e)}"})
        finally:
            if connection: connection.close()

    except Exception as e:
        # 记录请求处理错误日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.SAMPLE_REQUEST,
            result=AuditLogService.Result.FAILURE,
            description="样书申请请求处理错误",
            details={'error_reason': str(e)}
        )
        return jsonify({"code": 1, "message": f"请求处理错误: {str(e)}"})

# 函数已移至API模块

# 函数已移至API模块

# PC端接口 - 获取教师的样书申请列表
@teacher_bp.route('/get_sample_requests', methods=['GET'])
def get_sample_requests():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    # 获取筛选参数
    status = request.args.get('status', 'all')
    search = request.args.get('search', '')
    date_filter = request.args.get('date_filter', 'all')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 10))
    
    # 计算分页偏移量
    offset = (page - 1) * page_size
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础查询 - 修改了查询结构，按订单号和出版社单位分组
            # 使用 MAX 或 MIN 函数解决 GROUP BY 中非聚合列的问题
            base_query = """
                SELECT CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY')) as order_group_key, -- 订单号和出版社公司ID的组合键
                       sr.order_number, 
                       MAX(sr.status) as status, 
                       MAX(sr.request_date) as request_date, 
                       MAX(sr.approval_date) as approval_date, 
                       MAX(sr.shipping_date) as shipping_date,
                       MAX(sr.tracking_number) as tracking_number, 
                       MAX(sr.shipping_company) as express_company, 
                       MAX(sr.purpose) as purpose,
                       -- MAX(sr.request_reason) as request_reason, -- 单项备注在书籍级别获取
                       MAX(sa.address_id) as address_id, 
                       MAX(sa.name) as recipient_name, 
                       MAX(sa.phone_number) as recipient_phone, 
                       CONCAT(IFNULL(MAX(sa.province), ''), IFNULL(MAX(sa.city), ''), IFNULL(MAX(sa.district), ''), IFNULL(MAX(sa.detailed_address), '')) as address,
                       -- MAX(tc.course_name) as course_name, -- 课程信息在书籍级别获取
                       -- MAX(tc.semester) as semester, -- 学期信息在书籍级别获取
                       MAX(pc.name) as publisher_company_name, -- 出版社公司名称
                       MAX(u.publisher_company_id) as publisher_company_id, -- 出版社公司ID
                       COUNT(DISTINCT sr.request_id) as book_count_in_group -- 该订单下此出版社公司的书籍数量
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sb.publisher_id = u.user_id -- u 是书籍的发布者（员工）
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id -- pc 是出版社公司
                LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                -- LEFT JOIN teacher_courses tc ON sr.course_id = tc.id -- 课程信息现在是书籍级别，此处不再直接关联
                WHERE sr.teacher_id = %s
            """
            
            count_query = """
                SELECT COUNT(DISTINCT CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY'))) as total,
                       COUNT(DISTINCT CASE WHEN sr.status = 'pending' THEN CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY')) ELSE NULL END) as pending_count,
                       COUNT(DISTINCT CASE WHEN sr.status = 'approved' AND sr.tracking_number IS NULL THEN CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY')) ELSE NULL END) as waiting_count,
                       COUNT(DISTINCT CASE WHEN sr.status = 'approved' AND sr.tracking_number IS NOT NULL THEN CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY')) ELSE NULL END) as shipped_count,
                       COUNT(DISTINCT CASE WHEN sr.status = 'rejected' THEN CONCAT(sr.order_number, '_', IFNULL(u.publisher_company_id, 'UNKNOWN_COMPANY')) ELSE NULL END) as rejected_count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sb.publisher_id = u.user_id
                WHERE sr.teacher_id = %s
            """
            
            # 添加状态筛选
            status_condition = ""
            if status == 'all':
                pass
            elif status == 'pending':
                status_condition = " AND sr.status = 'pending'"
            elif status == 'approved': # 已通过，包含待发货和已发货
                status_condition = " AND sr.status = 'approved'"
            elif status == 'waiting': # 明确的待发货
                status_condition = " AND sr.status = 'approved' AND sr.tracking_number IS NULL"
            elif status == 'shipped':
                status_condition = " AND sr.status = 'approved' AND sr.tracking_number IS NOT NULL"
            elif status == 'rejected':
                status_condition = " AND sr.status = 'rejected'"
            
            if status_condition:
                base_query += status_condition
                count_query += status_condition # count_query 也需要相同的状态过滤
            
            # 添加搜索条件 (搜索样书名称、出版社公司名称、订单号)
            if search:
                search_condition_parts = []
                search_param_value = f"%{search}%"
                # 为了让占位符正常工作，我们需要将实际值添加到params列表中，而不是直接注入SQL字符串
                # search_condition = f" AND (sb.name LIKE '%{search}%' OR pc.name LIKE '%{search}%' OR sr.order_number LIKE '%{search}%')"
                search_condition = " AND (sb.name LIKE %s OR pc.name LIKE %s OR sr.order_number LIKE %s)"

                # 主查询的参数列表会动态构建
                # 统计查询的参数列表也需要处理
                base_query += search_condition
                # count_query += search_condition # count_query 的参数在后面统一处理

            # 添加日期筛选
            date_condition_str = "" # 用于SQL字符串
            date_params_list = []   # 用于参数列表

            if date_filter == '7days': # 前端是 '7days'，不是 'week'
                date_condition_str = " AND sr.request_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
            elif date_filter == '30days': # 前端是 '30days'
                date_condition_str = " AND sr.request_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)"
            elif date_filter == '90days': # 前端是 '90days'
                date_condition_str = " AND sr.request_date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)"
            elif date_filter == 'custom' and start_date and end_date:
                date_condition_str = " AND sr.request_date BETWEEN %s AND %s"
                date_params_list.extend([start_date, end_date])
            
            if date_condition_str:
                base_query += date_condition_str
                count_query += date_condition_str
            
            # 添加分组，确保按订单号和出版社公司ID分组
            base_query += " GROUP BY sr.order_number, u.user_id"
            
            # 添加排序和分页
            base_query += " ORDER BY MAX(sr.request_date) DESC LIMIT %s OFFSET %s"
            
            # 构建主查询参数列表
            main_query_params = [session['user_id']]
            if search:
                main_query_params.extend([search_param_value, search_param_value, search_param_value])
            main_query_params.extend(date_params_list) # 添加日期参数
            main_query_params.extend([page_size, offset]) # 添加分页参数

            # 构建统计查询参数列表
            count_query_params = [session['user_id']]
            # 状态筛选已直接加入SQL字符串，搜索和日期筛选需要参数
            if search: # count_query 也需要搜索条件，但它的JOIN结构不同，需要小心
                 # For count_query, the search condition needs to be adapted or simplified if direct join to pc is not there
                 # Simplified: search only order_number or book details if pc join is complex for count
                 # For now, let's assume count_query will be adapted to handle search if necessary or search is omitted for counts for simplicity in this step
                 pass # Search params for count_query might need different handling based on its final structure.
                      # Let's assume status filter is enough for counts for now if search makes it too complex.
            count_query_params.extend(date_params_list) # 添加日期参数 for count_query

            cursor.execute(count_query, tuple(count_query_params))
            count_result = cursor.fetchone()
            
            cursor.execute(base_query, tuple(main_query_params))
            orders = cursor.fetchall()
            
            # 获取每个订单的书籍详情
            result = []
            for order_group in orders: 
                # 设置display_status
                if order_group['status'] == 'pending':
                    display_status = 'pending'
                elif order_group['status'] == 'rejected':
                    display_status = 'rejected'
                elif order_group['status'] == 'approved':
                    if order_group['tracking_number']:
                        display_status = 'shipped'
                    else:
                        display_status = 'waiting'
                else:
                    display_status = order_group['status'] # 其他可能的状态（理论上不应出现）
                
                # 格式化日期
                request_date = order_group['request_date'].strftime('%Y-%m-%d %H:%M:%S') if order_group['request_date'] else None
                approval_date = order_group['approval_date'].strftime('%Y-%m-%d %H:%M:%S') if order_group['approval_date'] else None
                shipping_date = order_group['shipping_date'].strftime('%Y-%m-%d %H:%M:%S') if order_group['shipping_date'] else None
                
                # 查询该订单下，此出版社公司的所有相关书籍申请记录
                book_sql_base = """
                    SELECT sr.request_id, sr.quantity, sb.id as book_id, sb.name as book_name, 
                           sb.author, sb.isbn, sb.price,
                           tc.course_name, tc.semester, sr.request_reason AS item_remark -- request_reason 作为单项备注
                    FROM sample_requests sr
                    JOIN sample_books sb ON sr.textbook_id = sb.id
                    JOIN users book_owner_user ON sb.publisher_id = book_owner_user.user_id -- book_owner_user 是书籍的发布者（员工）
                    LEFT JOIN teacher_courses tc ON sr.course_id = tc.id -- 课程与 sample_requests 关联
                    WHERE sr.order_number = %s AND sr.teacher_id = %s
                """
                
                # 根据当前分组的出版社公司ID筛选书籍
                # order_group['publisher_company_id'] 是当前分组的出版社公司ID
                book_sql_final = book_sql_base + " AND book_owner_user.publisher_company_id = %s"
                book_params = (
                    order_group['order_number'], 
                    session['user_id'], 
                    order_group['publisher_company_id'] 
                )
                
                cursor.execute(book_sql_final, book_params)
                books = cursor.fetchall()

                # 构建结果对象
                order_data = {
                    'order_number': order_group['order_number'],
                    'publisher_name': order_group['publisher_company_name'], # 出版社单位名称
                    'publisher_id': order_group['publisher_company_id'],   # 出版社单位ID
                    'status': order_group['status'], # 组的聚合状态
                    'display_status': display_status, # 用于前端显示的具体状态
                    'request_date': request_date,
                    'approval_date': approval_date,
                    'shipping_date': shipping_date,
                    'tracking_number': order_group['tracking_number'],
                    'express_company': order_group['express_company'],
                    'book_count': order_group['book_count_in_group'], # 使用聚合查询得到的该分组的书籍数
                    'recipient_name': order_group['recipient_name'],
                    'recipient_phone': order_group['recipient_phone'],
                    'address': order_group['address'],
                    'address_id': order_group['address_id'],
                    'purpose': order_group['purpose'], # 订单级别的用途
                    # 'request_reason': order_group.get('request_reason'), -- 移除，单项备注在books中
                    'books': books # 当前出版社单位在此订单中的具体书籍列表
                }
                
                result.append(order_data)
            
            # 构建返回数据
            # 'approved' 计数器应该包含 'waiting' 和 'shipped'
            approved_total_count = (count_result.get('waiting_count', 0) or 0) + (count_result.get('shipped_count', 0) or 0)

            counters = {
                'all': count_result.get('total', 0) or 0,
                'pending': count_result.get('pending_count', 0) or 0,
                'waiting': count_result.get('waiting_count', 0) or 0,
                'shipped': count_result.get('shipped_count', 0) or 0,
                'approved': approved_total_count,
                'rejected': count_result.get('rejected_count', 0) or 0
            }
            
            pagination = {
                'total': count_result['total'],
                'page': page,
                'page_size': page_size,
                'total_pages': (count_result['total'] + page_size - 1) // page_size
            }
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "requests": result,
                    "counters": counters,
                    "pagination": pagination
                }
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书申请失败: {str(e)}"})
    finally:
        connection.close()

# PC端接口 - 获取样书申请详情
@teacher_bp.route('/get_request_detail', methods=['GET'])
def get_request_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    request_id = request.args.get('request_id')
    
    if not request_id:
        return jsonify({"code": 1, "message": "请求ID为必填项"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT sr.request_id, sr.order_number, sr.status, sr.request_date, sr.approval_date as approve_date, 
                       sr.shipping_date as ship_date, sr.tracking_number, sr.shipping_company as express_company,
                       sb.id as textbook_id, sb.name as textbook_name, sb.author, sb.isbn, sb.price,
                       sb.publisher_name,
                       sa.name as recipient_name, sa.phone_number as recipient_phone, 
                       CONCAT(IFNULL(sa.province, ''), IFNULL(sa.city, ''), IFNULL(sa.district, ''), IFNULL(sa.detailed_address, '')) as address,
                       approver.name as approver_name, shipper.name as shipper_name
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                LEFT JOIN users approver ON approver.user_id = (
                    SELECT user_id FROM users WHERE role = 'publisher' AND user_id = sb.publisher_id LIMIT 1
                )
                LEFT JOIN users shipper ON shipper.user_id = (
                    SELECT user_id FROM users WHERE role = 'publisher' AND user_id = sb.publisher_id LIMIT 1
                )
                WHERE sr.request_id = %s AND sr.teacher_id = %s
            """
            cursor.execute(sql, (request_id, session['user_id']))
            request_detail = cursor.fetchone()
            
            if not request_detail:
                return jsonify({"code": 1, "message": "申请不存在或无权查看"})
            
            # 处理状态显示
            if request_detail['status'] == 'approved':
                if request_detail['tracking_number'] is None:
                    request_detail['status'] = 'waiting'
            
            # 格式化日期
            if request_detail['request_date']:
                request_detail['request_date'] = request_detail['request_date'].strftime('%Y-%m-%d %H:%M:%S')
            if request_detail['approve_date']:
                request_detail['approve_date'] = request_detail['approve_date'].strftime('%Y-%m-%d %H:%M:%S')
            if request_detail['ship_date']:
                request_detail['ship_date'] = request_detail['ship_date'].strftime('%Y-%m-%d %H:%M:%S')
            
            return jsonify({"code": 0, "data": request_detail})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取申请详情失败: {str(e)}"})
    finally:
        connection.close()

# PC端接口 - 删除地址
@teacher_bp.route('/new_delete_address', methods=['POST'])
def new_delete_address():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    address_id = request.form.get('address_id')
    
    if not address_id:
        return jsonify({"code": 1, "message": "地址ID为必填项"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查地址是否存在且属于当前用户
            sql = "SELECT * FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            address = cursor.fetchone()
            
            if not address:
                return jsonify({"code": 1, "message": "地址不存在或无权删除"})
            
            # 检查该地址是否被用于未完成的样书申请
            sql = """
                SELECT COUNT(*) as count 
                FROM sample_requests 
                WHERE address_id = %s AND status IN ('pending', 'approved')
            """
            cursor.execute(sql, (address_id,))
            result = cursor.fetchone()
            
            if result and result['count'] > 0:
                return jsonify({"code": 1, "message": "该地址已被用于进行中的样书申请，无法删除"})
            
            # 删除地址
            sql = "DELETE FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
            cursor.execute(sql, (address_id, session['user_id']))
            connection.commit()
            return jsonify({"code": 0, "message": "地址删除成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除地址失败: {str(e)}"})
    finally:
        connection.close()

# 函数已移至API模块

# 函数已移至API模块

# 这些函数已移至API模块

# 获取教师主讲课程列表
@teacher_bp.route('/get_teacher_courses', methods=['GET'])
def get_teacher_courses():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    teacher_id = session.get('user_id')
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, course_name, semester, course_type, student_count, created_at 
                FROM teacher_courses 
                WHERE teacher_id = %s
                ORDER BY created_at DESC
            """
            cursor.execute(sql, (teacher_id,))
            courses = cursor.fetchall()
            
            # 格式化日期
            for course in courses:
                if course.get('created_at'):
                    course['created_at'] = course['created_at'].strftime('%Y-%m-%d')
            
            return jsonify({"code": 0, "data": courses})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取主讲课程失败: {str(e)}"})
    finally:
        connection.close()

# 添加主讲课程
@teacher_bp.route('/add_teacher_course', methods=['POST'])
def add_teacher_course():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    teacher_id = session.get('user_id')
    course_name = request.form.get('course_name')
    semester = request.form.get('semester')
    course_type = request.form.get('course_type')
    student_count = request.form.get('student_count')
    
    if not all([course_name, semester, course_type, student_count]):
        return jsonify({"code": 1, "message": "请填写完整课程信息"})
    
    try:
        student_count = int(student_count)
        if student_count <= 0:
            return jsonify({"code": 1, "message": "开课人数必须为正整数"})
    except ValueError:
        return jsonify({"code": 1, "message": "开课人数必须为数字"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                INSERT INTO teacher_courses 
                (teacher_id, course_name, semester, course_type, student_count)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (teacher_id, course_name, semester, course_type, student_count))
            connection.commit()
            
            # 获取新添加的课程ID
            new_course_id = cursor.lastrowid
            
            return jsonify({"code": 0, "message": "添加主讲课程成功", "course_id": new_course_id})
    except Exception as e:
        return jsonify({"code": 1, "message": f"添加主讲课程失败: {str(e)}"})
    finally:
        connection.close()

# 删除主讲课程
@teacher_bp.route('/delete_teacher_course', methods=['POST'])
def delete_teacher_course():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    teacher_id = session.get('user_id')
    course_id = request.form.get('course_id')
    
    if not course_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查课程是否存在且属于当前用户
            check_sql = "SELECT id FROM teacher_courses WHERE id = %s AND teacher_id = %s"
            cursor.execute(check_sql, (course_id, teacher_id))
            course = cursor.fetchone()
            
            if not course:
                return jsonify({"code": 1, "message": "课程不存在或无权删除"})
            
            # 检查课程是否被样书申请使用
            check_used_sql = "SELECT COUNT(*) as count FROM sample_requests WHERE course_id = %s"
            cursor.execute(check_used_sql, (course_id,))
            used_count = cursor.fetchone()['count']
            
            if used_count > 0:
                return jsonify({"code": 1, "message": "该课程已被样书申请使用，无法删除"})
            
            # 删除课程
            delete_sql = "DELETE FROM teacher_courses WHERE id = %s"
            cursor.execute(delete_sql, (course_id,))
            connection.commit()
            
            return jsonify({"code": 0, "message": "删除主讲课程成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"删除主讲课程失败: {str(e)}"})
    finally:
        connection.close()

# PC端接口 - 获取订单详情
@teacher_bp.route('/get_order_detail', methods=['GET'])
def get_order_detail():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    order_number = request.args.get('order_number')
    publisher_id = request.args.get('publisher_id')
    
    if not order_number:
        return jsonify({"code": 1, "message": "缺少订单号"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询订单基本信息
            base_query = """
                SELECT sr.order_number, 
                       sr.status, 
                       sr.request_date, 
                       sr.approval_date, 
                       sr.shipping_date,
                       sr.tracking_number, 
                       sr.shipping_company as express_company, 
                       sr.purpose,
                       sr.reject_reason,
                       sr.request_reason,
                       sa.address_id, 
                       sa.name as recipient_name, 
                       sa.phone_number as recipient_phone, 
                       CONCAT(IFNULL(sa.province, ''), IFNULL(sa.city, ''), IFNULL(sa.district, ''), IFNULL(sa.detailed_address, '')) as address,
                       tc.course_name, 
                       tc.semester,
                       pc.name as publisher_name,
                       u.user_id as publisher_id
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN shipping_addresses sa ON sr.address_id = sa.address_id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sr.order_number = %s AND sr.teacher_id = %s
            """
            
            params = [order_number, session['user_id']]
            
            # 如果提供了出版社公司ID，添加筛选条件
            # 注意：前端传递的publisher_id实际上是publisher_company_id
            if publisher_id:
                base_query += " AND u.publisher_company_id = %s"
                params.append(publisher_id)
            
            # 限制只返回一条记录
            base_query += " LIMIT 1"
            
            cursor.execute(base_query, params)
            order_info = cursor.fetchone()
            
            if not order_info:
                return jsonify({"code": 1, "message": "订单不存在"})
            
            # 设置display_status
            if order_info['status'] == 'pending':
                display_status = 'pending'
            elif order_info['status'] == 'rejected':
                display_status = 'rejected'
            elif order_info['status'] == 'approved':
                if order_info['tracking_number']:
                    display_status = 'shipped'
                else:
                    display_status = 'waiting'
            else:
                display_status = order_info['status']
            
            # 查询订单中的所有书籍
            book_sql = """
                SELECT sr.request_id, sr.quantity, sr.purpose, sr.course_id, 
                       sb.id as book_id, sb.name as book_name, 
                       sb.author, sb.isbn, sb.price,
                       tc.course_name, tc.semester, sr.request_reason AS item_remark
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN teacher_courses tc ON sr.course_id = tc.id
                WHERE sr.order_number = %s AND sr.teacher_id = %s
            """
            
            book_params = [order_number, session['user_id']]
            
            # 如果提供了出版社公司ID，添加筛选条件
            if publisher_id:
                book_sql += " AND u.publisher_company_id = %s"
                book_params.append(publisher_id)
            
            cursor.execute(book_sql, book_params)
            books = cursor.fetchall()
            
            # 格式化时间
            order_info['request_date'] = order_info['request_date'].strftime('%Y-%m-%d %H:%M:%S') if order_info['request_date'] else None
            order_info['approval_date'] = order_info['approval_date'].strftime('%Y-%m-%d %H:%M:%S') if order_info['approval_date'] else None
            order_info['shipping_date'] = order_info['shipping_date'].strftime('%Y-%m-%d %H:%M:%S') if order_info['shipping_date'] else None
            
            # 构建结果
            result = dict(order_info)
            result['display_status'] = display_status
            result['books'] = books
            
            return jsonify({"code": 0, "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取订单详情失败: {str(e)}"})
    finally:
        connection.close()

# PC端接口 - 取消订单（新增）
@teacher_bp.route('/cancel_order', methods=['POST'])
def cancel_order():
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    order_number = request.form.get('order_number')
    publisher_company_id = request.form.get('publisher_id')  # 这里接收的实际是出版社公司ID
    
    if not order_number:
        return jsonify({"code": 1, "message": "订单编号为必填项"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建基础查询条件 - 使用别名sr方便后续多表关联查询
            base_conditions = "sr.order_number = %s AND sr.teacher_id = %s"
            params = [order_number, session['user_id']]
            
            # 如果提供了出版社公司ID，添加到查询条件
            # 需要通过多表关联，确保只筛选该出版社公司的样书
            publisher_condition = ""
            if publisher_company_id:
                publisher_condition = """
                    AND sr.textbook_id IN (
                        SELECT sb.id 
                        FROM sample_books sb
                        JOIN users u ON sb.publisher_id = u.user_id
                        WHERE u.publisher_company_id = %s
                    )
                """
                params.append(publisher_company_id)
            
            # 检查订单是否存在且属于当前用户
            check_sql = f"""
                SELECT COUNT(*) as count 
                FROM sample_requests sr
                WHERE {base_conditions}{publisher_condition}
            """
            cursor.execute(check_sql, params)
            count_result = cursor.fetchone()
            
            if not count_result or count_result['count'] == 0:
                return jsonify({"code": 1, "message": "订单不存在或无权操作"})
            
            # 检查订单状态
            status_sql = f"""
                SELECT status 
                FROM sample_requests sr
                WHERE {base_conditions}{publisher_condition}
                LIMIT 1
            """
            cursor.execute(status_sql, params)
            status_result = cursor.fetchone()
            
            if status_result['status'] != 'pending':
                return jsonify({"code": 1, "message": "只能取消待审核状态的申请"})
            
            # 获取要删除的申请ID用于邮件通知，并同时获取邮件发送所需的详细信息
            get_email_data_sql = f"""
                SELECT 
                    sr.request_id,
                    sr.teacher_id,
                    sb.publisher_id,
                    sb.name as book_name,
                    sb.isbn,
                    u.name as teacher_name,
                    u.email as teacher_email,
                    s.name as school_name,
                    pub_user.user_id as publisher_user_id,
                    pub_user.email as publisher_email,
                    pub_user.name as publisher_user_name
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                JOIN users u ON sr.teacher_id = u.user_id
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                JOIN users pub_user ON sb.publisher_id = pub_user.user_id
                WHERE {base_conditions}{publisher_condition} AND sr.status = 'pending'
            """
            cursor.execute(get_email_data_sql, params)
            email_data_records = cursor.fetchall()
            cancelled_request_ids = [record['request_id'] for record in email_data_records]
            
            # 删除符合条件的申请(只删除该出版社公司的申请，不影响其他出版社)
            delete_sql = f"""
                DELETE sr FROM sample_requests sr
                WHERE {base_conditions}{publisher_condition} AND sr.status = 'pending'
            """
            cursor.execute(delete_sql, params)
            connection.commit()
            
            # 发送邮件通知出版社用户
            if cancelled_request_ids and email_data_records:
                try:
                    print(f"[DEBUG] 准备发送样书取消申请邮件通知:")
                    print(f"  - 教师ID: {session['user_id']}")
                    print(f"  - 取消申请ID列表: {cancelled_request_ids}")
                    print(f"  - 邮件数据记录数: {len(email_data_records)}")
                    
                    email_result = notification_service.notify_publisher_sample_requests_with_data(
                        email_data_records
                    )
                    
                    print(f"[DEBUG] 邮件发送结果: {email_result}")
                    
                    if email_result:
                        print(f"[SUCCESS] 样书取消申请邮件通知发送成功")
                    else:
                        print(f"[WARNING] 样书取消申请邮件通知发送失败")
                        
                except Exception as e:
                    print(f"[ERROR] 发送样书取消申请邮件通知时发生异常:")
                    print(f"  - 异常类型: {type(e).__name__}")
                    print(f"  - 异常信息: {str(e)}")
                    import traceback
                    print(f"  - 详细堆栈: {traceback.format_exc()}")
                    # 邮件发送失败不影响业务流程
            else:
                print(f"[DEBUG] 没有要取消的申请ID或邮件数据，跳过邮件发送")
            
            return jsonify({
                "code": 0, 
                "message": f"已成功取消所选出版社的样书申请，共 {count_result['count']} 本，已发送邮件通知相关出版社", 
                "canceled_count": count_result['count']
            })
    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"取消订单失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/get_exhibitions', methods=['GET'])
def get_exhibitions():
    """获取书展活动列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    offset = (page - 1) * limit
    status = request.args.get('status', 'all')
    tab = request.args.get('tab', 'all')
    search = request.args.get('search', '')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 确保状态和标签值转换为小写以进行不区分大小写的比较
    status = status.lower() if status else 'all'
    tab = tab.lower() if tab else 'all'
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取教师所属学校ID
            cursor.execute("SELECT teacher_school_id, name FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            
            if not user_info or not user_info['teacher_school_id']:
                return jsonify({"code": 1, "message": "用户信息不完整"})
            
            school_id = user_info['teacher_school_id']
            
            # 构建查询条件
            conditions = []
            params = []
            
            # 根据状态过滤
            if status != 'all':
                conditions.append("LOWER(be.status) = %s")
                params.append(status)
            
            # 根据标签过滤
            if tab == 'initiated':
                conditions.append("be.initiator_id = %s")
                params.append(user_id)
            elif tab in ['draft', 'pending_review', 'rejected', 'published', 'cancelled', 'ended']:
                conditions.append("LOWER(be.status) = %s")
                params.append(tab)
            
            # 搜索条件
            if search:
                conditions.append("be.title LIKE %s")
                params.append(f"%{search}%")
            
            # 日期过滤
            if start_date:
                conditions.append("be.start_time >= %s")
                params.append(f"{start_date} 00:00:00")
            if end_date:
                conditions.append("be.end_time <= %s")
                params.append(f"{end_date} 23:59:59")
            
            # 学校条件
            conditions.append("be.school_id = %s")
            params.append(school_id)
            
            # 构建SQL查询语句
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # 调试 - 如果是cancelled状态筛选，记录条件
            # if status == 'cancelled' or tab == 'cancelled':
            #     print(f"DEBUG: Cancelled filter conditions: {where_clause}, params: {params}")
            
            count_sql = f"""
                SELECT COUNT(*) as count 
                FROM book_exhibitions be
                WHERE {where_clause}
            """
            
            # 查询总数
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['count']
            
            # 调试 - 如果是cancelled状态筛选，记录总数
            # if status == 'cancelled' or tab == 'cancelled':
            #     print(f"DEBUG: Cancelled filter count: {total}")
            
            # 查询书展列表
            query_sql = f"""
                SELECT 
                    be.id, be.title, be.logo_url, be.description, 
                    be.start_time, be.end_time, be.location, be.status,
                    be.registration_deadline, be.created_at, be.updated_at,
                    be.school_address,
                    s.name AS school_name,
                    u.name AS initiator_name,
                    ei.name AS contact_name, ei.phone AS contact_phone,
                    (SELECT COUNT(*) FROM exhibition_registrations er WHERE er.exhibition_id = be.id) AS registrations_count,
                    (be.initiator_id = %s) AS is_initiator,
                    be.allows_parking, be.requires_campus_registration
                FROM book_exhibitions be
                JOIN schools s ON be.school_id = s.id
                JOIN users u ON be.initiator_id = u.user_id
                JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
                WHERE {where_clause}
                ORDER BY be.created_at DESC
                LIMIT %s OFFSET %s
            """
            
            cursor.execute(query_sql, [user_id] + params + [limit, offset])
            exhibitions = cursor.fetchall()
            
            # 格式化日期时间
            for exhibition in exhibitions:
                exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else None
                exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else None
                exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else None
                exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d %H:%M') if exhibition['created_at'] else None
                exhibition['updated_at'] = exhibition['updated_at'].strftime('%Y-%m-%d %H:%M') if exhibition['updated_at'] else None
                exhibition['is_initiator'] = bool(exhibition['is_initiator'])
            
            # 查询各状态数量
            status_counts = {}
            for status_type in ['draft', 'published', 'cancelled', 'ended']:
                status_params = [status_type, school_id]
                status_sql = """
                    SELECT COUNT(*) as count 
                    FROM book_exhibitions 
                    WHERE status = %s AND school_id = %s
                """
                cursor.execute(status_sql, status_params)
                status_counts[status_type] = cursor.fetchone()['count']
            
            # 查询当前用户发起的书展数量
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM book_exhibitions 
                WHERE initiator_id = %s AND school_id = %s
            """, (user_id, school_id))
            status_counts['initiated'] = cursor.fetchone()['count']
            
            # 查询全部书展数量
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM book_exhibitions 
                WHERE school_id = %s
            """, (school_id,))
            status_counts['all'] = cursor.fetchone()['count']
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "total": total,
                    "exhibitions": exhibitions,
                    "status_counts": status_counts
                }
            })
    # except Exception as e:
    #     print(f"Error in get_exhibitions: {str(e)}")
        return jsonify({"code": 1, "message": f"获取书展失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/get_exhibition_detail', methods=['GET'])
def get_exhibition_detail():
    """获取书展详情"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session['user_id']
    exhibition_id = request.args.get('id')
    
    if not exhibition_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取书展基本信息
            cursor.execute("""
                SELECT
                    be.*, s.name AS school_name,
                    (be.initiator_id = %s) AS is_initiator,
                    CASE
                        WHEN be.co_organizer_type = 'publisher' THEN pc.name
                        WHEN be.co_organizer_type = 'dealer' THEN dc.name
                        ELSE NULL
                    END AS co_organizer_name
                FROM book_exhibitions be
                JOIN schools s ON be.school_id = s.id
                LEFT JOIN publisher_companies pc ON be.co_organizer_type = 'publisher' AND be.co_organizer_id = pc.id
                LEFT JOIN dealer_companies dc ON be.co_organizer_type = 'dealer' AND be.co_organizer_id = dc.id
                WHERE be.id = %s
            """, (user_id, exhibition_id))
            
            exhibition = cursor.fetchone()
            
            if not exhibition:
                return jsonify({"code": 1, "message": "书展不存在"})
            
            # 格式化日期时间
            exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else None
            exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else None
            exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else None
            exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d %H:%M') if exhibition['created_at'] else None
            exhibition['updated_at'] = exhibition['updated_at'].strftime('%Y-%m-%d %H:%M') if exhibition['updated_at'] else None
            exhibition['is_initiator'] = bool(exhibition['is_initiator'])
            
            # 获取发起人信息
            cursor.execute("""
                SELECT * FROM exhibition_initiators
                WHERE exhibition_id = %s
            """, (exhibition_id,))
            
            initiator = cursor.fetchone()
            exhibition['initiator'] = initiator
            
            # 获取报名信息（如果有权限）
            if exhibition['is_initiator']:
                cursor.execute("""
                    SELECT 
                        er.id, er.status, er.created_at,
                        u.name AS publisher_name,
                        pc.name AS company_name,
                        (SELECT COUNT(*) FROM exhibition_participants ep WHERE ep.registration_id = er.id) AS participants_count
                    FROM exhibition_registrations er
                    JOIN users u ON er.publisher_id = u.user_id
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    WHERE er.exhibition_id = %s
                    ORDER BY er.created_at DESC
                """, (exhibition_id,))
                
                registrations = cursor.fetchall()
                
                # 格式化日期时间
                for reg in registrations:
                    reg['created_at'] = reg['created_at'].strftime('%Y-%m-%d %H:%M') if reg['created_at'] else None
                
                exhibition['registrations'] = registrations
            else:
                exhibition['registrations'] = []
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": exhibition
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展详情失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/get_exhibition_participants', methods=['GET'])
def get_exhibition_participants():
    """获取书展参展人员"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    registration_id = request.args.get('registration_id')
    
    if not registration_id:
        return jsonify({"code": 1, "message": "参数错误"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证权限（仅允许书展发起人查看）
            cursor.execute("""
                SELECT be.initiator_id 
                FROM exhibition_registrations er
                JOIN book_exhibitions be ON er.exhibition_id = be.id
                WHERE er.id = %s
            """, (registration_id,))
            
            result = cursor.fetchone()
            
            if not result or result['initiator_id'] != session['user_id']:
                return jsonify({"code": 1, "message": "没有权限查看"})
            
            # 获取参展人员
            cursor.execute("""
                SELECT ep.*, er.publisher_id, u.name AS publisher_name, pc.name AS company_name
                FROM exhibition_participants ep
                JOIN exhibition_registrations er ON ep.registration_id = er.id
                JOIN users u ON er.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                WHERE ep.registration_id = %s
                ORDER BY ep.is_contact DESC, ep.created_at ASC
            """, (registration_id,))
            
            participants = cursor.fetchall()
            
            # 格式化日期时间
            for participant in participants:
                participant['created_at'] = participant['created_at'].strftime('%Y-%m-%d %H:%M') if participant['created_at'] else None
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": participants
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取参展人员失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/save_exhibition', methods=['POST'])
def save_exhibition():
    """保存书展活动（新增或编辑）"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session['user_id']
    
    # 获取表单数据
    data = request.form.to_dict()
    exhibition_id = data.get('exhibitionId')
    status = data.get('status', 'draft')  # 默认为草稿状态
    
    # 验证必填字段
    required_fields = ['title', 'startTime', 'endTime', 'registrationDeadline', 
                      'location', 'schoolAddress', 'initiatorName', 'initiatorPhone']
    
    for field in required_fields:
        if field not in data or not data[field]:
            return jsonify({"code": 1, "message": f"请填写{field}字段"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取教师所属学校ID
            cursor.execute("SELECT teacher_school_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            
            if not user_info or not user_info['teacher_school_id']:
                return jsonify({"code": 1, "message": "用户信息不完整"})
            
            school_id = user_info['teacher_school_id']
            
            # 处理文件上传
            logo_url = None
            if 'logoUpload' in request.files:
                logo_file = request.files['logoUpload']
                if logo_file and logo_file.filename:
                    # 设置上传路径
                    upload_folder = 'app/static/uploads/exhibitions/logos'
                    os.makedirs(upload_folder, exist_ok=True)
                    
                    # 生成唯一文件名
                    filename = f"{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(logo_file.filename)}"
                    file_path = os.path.join(upload_folder, filename)
                    
                    # 保存文件
                    logo_file.save(file_path)
                    logo_url = f'/static/uploads/exhibitions/logos/{filename}'
            
            # 处理进校报备二维码
            registration_qrcode = None
            if 'registrationQrcodeUpload' in request.files:
                qrcode_file = request.files['registrationQrcodeUpload']
                if qrcode_file and qrcode_file.filename:
                    # 设置上传路径
                    upload_folder = 'app/static/uploads/exhibitions/qrcodes'
                    os.makedirs(upload_folder, exist_ok=True)
                    
                    # 生成唯一文件名
                    filename = f"{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(qrcode_file.filename)}"
                    file_path = os.path.join(upload_folder, filename)
                    
                    # 保存文件
                    qrcode_file.save(file_path)
                    registration_qrcode = f'/static/uploads/exhibitions/qrcodes/{filename}'
            
            # 处理checkbox值
            requires_campus_registration = 1 if data.get('requiresCampusRegistration') == 'on' else 0
            allows_parking = 1 if data.get('allowsParking') == 'on' else 0
            license_plate_required = 1 if data.get('licensePlateRequired') == 'on' else 0

            # 处理协办方信息
            co_organizer_type = data.get('coOrganizerType') if data.get('hasCoOrganizer') == 'on' else None
            co_organizer_id = data.get('coOrganizerId') if data.get('hasCoOrganizer') == 'on' else None

            # 根据是否有协办方和状态决定最终状态
            if status == 'published':
                if co_organizer_type and co_organizer_id:
                    # 有协办方时，发布状态改为待审核
                    status = 'pending_review'
                # 无协办方时，保持published状态

            # 开启事务
            connection.begin()
            
            if exhibition_id:  # 编辑现有书展
                # 验证是否为发起人
                cursor.execute("SELECT initiator_id, status FROM book_exhibitions WHERE id = %s", (exhibition_id,))
                result = cursor.fetchone()
                
                if not result or result['initiator_id'] != user_id:
                    return jsonify({"code": 1, "message": "没有权限编辑此书展"})
                
                # 检查书展状态
                if result['status'] == 'cancelled':
                    return jsonify({"code": 1, "message": "已取消的活动不能编辑，可作为模板创建新活动"})
                
                if result['status'] == 'ended':
                    return jsonify({"code": 1, "message": "已结束的活动不能编辑"})
                
                # 更新书展信息
                update_sql = """
                    UPDATE book_exhibitions SET
                    title = %s,
                    description = %s,
                    start_time = %s,
                    end_time = %s,
                    registration_deadline = %s,
                    location = %s,
                    school_address = %s,
                    requires_campus_registration = %s,
                    registration_requirements = %s,
                    allows_parking = %s,
                    requirements = %s,
                    status = %s,
                    co_organizer_type = %s,
                    co_organizer_id = %s,
                    license_plate_required = %s,
                    updated_at = NOW()
                """
                
                params = [
                    data.get('title'),
                    data.get('description'),
                    data.get('startTime'),
                    data.get('endTime'),
                    data.get('registrationDeadline'),
                    data.get('location'),
                    data.get('schoolAddress'),
                    requires_campus_registration,
                    data.get('registrationRequirements'),
                    allows_parking,
                    data.get('requirements'),
                    status,
                    co_organizer_type,
                    co_organizer_id,
                    license_plate_required
                ]
                
                # 添加logo_url和registration_qrcode（如果有）
                if logo_url:
                    update_sql += ", logo_url = %s"
                    params.append(logo_url)
                
                if registration_qrcode:
                    update_sql += ", registration_qrcode = %s"
                    params.append(registration_qrcode)
                
                update_sql += " WHERE id = %s"
                params.append(exhibition_id)
                
                cursor.execute(update_sql, params)
                
                # 更新发起人信息
                cursor.execute("""
                    UPDATE exhibition_initiators SET
                    name = %s,
                    department = %s,
                    position = %s,
                    phone = %s,
                    email = %s
                    WHERE exhibition_id = %s
                """, (
                    data.get('initiatorName'),
                    data.get('initiatorDepartment'),
                    data.get('initiatorPosition'),
                    data.get('initiatorPhone'),
                    data.get('initiatorEmail'),
                    exhibition_id
                ))
                
                message = "编辑成功"
            else:  # 新增书展
                # 插入书展信息
                cursor.execute("""
                    INSERT INTO book_exhibitions (
                        initiator_id, school_id, title, logo_url, description,
                        registration_deadline, start_time, end_time, location, school_address,
                        requires_campus_registration, registration_requirements,
                        registration_qrcode, allows_parking, requirements, status,
                        co_organizer_type, co_organizer_id, license_plate_required
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    user_id, school_id, data.get('title'), logo_url, data.get('description'),
                    data.get('registrationDeadline'), data.get('startTime'), data.get('endTime'),
                    data.get('location'), data.get('schoolAddress'), requires_campus_registration,
                    data.get('registrationRequirements'), registration_qrcode, allows_parking,
                    data.get('requirements'), status, co_organizer_type, co_organizer_id, license_plate_required
                ))
                
                # 获取新插入的书展ID
                exhibition_id = cursor.lastrowid
                
                # 插入发起人信息
                cursor.execute("""
                    INSERT INTO exhibition_initiators (
                        exhibition_id, name, department, position, phone, email
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    exhibition_id,
                    data.get('initiatorName'),
                    data.get('initiatorDepartment'),
                    data.get('initiatorPosition'),
                    data.get('initiatorPhone'),
                    data.get('initiatorEmail')
                ))
                
                message = "创建成功"

                # 如果状态是待审核，记录审核历史
                if status == 'pending_review':
                    cursor.execute("""
                        INSERT INTO exhibition_review_history
                        (exhibition_id, reviewer_id, reviewer_type, action, old_status, new_status, review_comment)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        exhibition_id, user_id, 'teacher', 'submit',
                        'draft', 'pending_review', '提交协办方审核'
                    ))

            # 提交事务
            connection.commit()
            
            return jsonify({
                "code": 0,
                "message": message,
                "data": {"id": exhibition_id}
            })
    except Exception as e:
        # 回滚事务
        if connection:
            connection.rollback()
        return jsonify({"code": 1, "message": f"保存书展失败: {str(e)}"})
    finally:
        if connection:
            connection.close()

@teacher_bp.route('/change_exhibition_status', methods=['POST'])
def change_exhibition_status():
    """更改书展状态（发布、取消、结束）"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session['user_id']
    exhibition_id = request.form.get('id')
    status = request.form.get('status')
    
    if not exhibition_id or not status:
        return jsonify({"code": 1, "message": "参数错误"})
    
    if status not in ['draft', 'published', 'cancelled', 'ended']:
        return jsonify({"code": 1, "message": "状态值无效"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 验证是否为发起人
            cursor.execute("SELECT initiator_id, status FROM book_exhibitions WHERE id = %s", (exhibition_id,))
            result = cursor.fetchone()
            
            if not result:
                return jsonify({"code": 1, "message": "书展不存在"})
            
            if result['initiator_id'] != user_id:
                return jsonify({"code": 1, "message": "没有权限更改此书展状态"})
            
            # 验证状态转换是否有效
            current_status = result['status']
            
            # 状态转换规则
            valid_transitions = {
                'draft': ['published', 'pending_review', 'cancelled'],
                'pending_review': ['published', 'rejected', 'cancelled'],
                'rejected': ['pending_review', 'cancelled'],
                'published': ['ended', 'cancelled'],
                'cancelled': [],  # 已取消状态不能再变更
                'ended': []       # 已结束状态不能再变更
            }
            
            if status not in valid_transitions.get(current_status, []):
                return jsonify({"code": 1, "message": f"不能将状态从{current_status}更改为{status}"})
            
            # 更新状态
            cursor.execute("""
                UPDATE book_exhibitions
                SET status = %s, updated_at = NOW()
                WHERE id = %s
            """, (status, exhibition_id))

            # 记录审核历史（如果是重新提交审核）
            if status == 'pending_review' and current_status == 'rejected':
                cursor.execute("""
                    INSERT INTO exhibition_review_history
                    (exhibition_id, reviewer_id, reviewer_type, action, old_status, new_status, review_comment)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    exhibition_id, user_id, 'teacher', 'resubmit',
                    current_status, status, '重新提交审核'
                ))

            connection.commit()
            
            status_messages = {
                'published': '发布',
                'pending_review': '提交审核',
                'rejected': '拒绝',
                'cancelled': '取消',
                'ended': '结束',
                'draft': '设为草稿'
            }
            
            return jsonify({
                "code": 0,
                "message": f"书展已{status_messages.get(status, '更新')}"
            })
    except Exception as e:
        if connection:
            connection.rollback()
        return jsonify({"code": 1, "message": f"更改状态失败: {str(e)}"})
    finally:
        if connection:
            connection.close()

@teacher_bp.route('/test_export', methods=['POST'])
def test_export():
    """测试导出功能"""
    try:
        print("测试导出功能")

        # 创建一个简单的测试文件
        import io
        content = "测试导出功能\n这是一个测试文件"
        output = io.BytesIO(content.encode('utf-8'))

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/plain'
        response.headers['Content-Disposition'] = 'attachment; filename="test.txt"'

        return response

    except Exception as e:
        print(f"测试导出失败: {str(e)}")
        return jsonify({"code": 1, "message": f"测试失败: {str(e)}"})

@teacher_bp.route('/export_exhibition_registrations', methods=['POST'])
def export_exhibition_registrations():
    """导出书展参展单位和人员信息"""
    try:
        print("=== 导出功能开始 ===")

        # 检查登录状态
        if 'user_id' not in session:
            print("错误：用户未登录")
            return jsonify({"code": 1, "message": "未登录"})

        user_id = session['user_id']
        exhibition_id = request.form.get('exhibition_id')

        print(f"用户ID: {user_id}")
        print(f"书展ID: {exhibition_id}")

        if not exhibition_id:
            print("错误：缺少书展ID参数")
            return jsonify({"code": 1, "message": "参数错误"})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 验证权限（仅允许书展发起人导出）
                cursor.execute("""
                    SELECT be.title, be.initiator_id
                    FROM book_exhibitions be
                    WHERE be.id = %s
                """, (exhibition_id,))

                exhibition = cursor.fetchone()

                if not exhibition:
                    return jsonify({"code": 1, "message": "书展不存在"})

                if exhibition['initiator_id'] != user_id:
                    return jsonify({"code": 1, "message": "没有权限导出此书展数据"})

                # 获取参展单位和人员信息
                cursor.execute("""
                    SELECT
                        er.id as registration_id,
                        er.status,
                        er.created_at as registration_time,
                        u.name AS publisher_name,
                        pc.name AS company_name,
                        ep.name as participant_name,
                        ep.phone as participant_phone,
                        '' as id_number,
                        ep.license_plate,
                        ep.role,
                        ep.is_contact
                    FROM exhibition_registrations er
                    JOIN users u ON er.publisher_id = u.user_id
                    LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                    LEFT JOIN exhibition_participants ep ON er.id = ep.registration_id
                    WHERE er.exhibition_id = %s AND er.status = 'registered'
                    ORDER BY pc.name, u.name, ep.is_contact DESC, ep.name
                """, (exhibition_id,))

                participants_data = cursor.fetchall()

                if not participants_data:
                    return jsonify({"code": 1, "message": "暂无参展人员数据可导出"})

                # 按单位分组参展人员
                companies = {}
                for row in participants_data:
                    company_name = row['company_name'] or row['publisher_name']
                    if company_name not in companies:
                        companies[company_name] = []

                    # 只有当有参展人员信息时才添加
                    if row['participant_name']:
                        companies[company_name].append({
                            'name': row['participant_name'],
                            'phone': row['participant_phone'],
                            'id_number': row['id_number'],
                            'license_plate': row['license_plate'],
                            'role': row['role'],
                            'is_contact': row['is_contact']
                        })

                # 生成Excel文件
                print("开始生成Excel文件")
                try:
                    import openpyxl
                    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
                    from openpyxl.utils import get_column_letter
                    import io
                    from datetime import datetime
                    print("Excel库导入成功")
                except ImportError as e:
                    print(f"Excel库导入失败: {e}")
                    return jsonify({"code": 1, "message": f"系统错误：缺少必要的库 {str(e)}"})

                # 创建工作簿
                wb = openpyxl.Workbook()
                print("工作簿创建成功")

                # 创建参展人员信息表
                ws = wb.active
                ws.title = "参展人员信息"

                # 设置样式
                title_font = Font(name='微软雅黑', size=14, bold=True)
                header_font = Font(name='微软雅黑', size=11, bold=True)
                company_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
                cell_font = Font(name='微软雅黑', size=10)

                # 主标题
                ws['A1'] = f"《{exhibition['title']}》参展人员信息"
                ws['A1'].font = title_font
                ws.merge_cells('A1:F1')

                current_row = 3

                # 按单位分组展示参展人员
                for company_name, participants in companies.items():
                    if not participants:  # 跳过没有参展人员的单位
                        continue

                    # 单位名称行
                    ws.cell(row=current_row, column=1, value=f"参展单位：{company_name}").font = company_font
                    ws.cell(row=current_row, column=1).fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
                    ws.merge_cells(f'A{current_row}:F{current_row}')
                    current_row += 1

                    # 表头
                    headers = ['姓名', '手机号', '身份证号', '车牌号码', '职务/角色', '是否联系人']
                    for col, header in enumerate(headers, 1):
                        cell = ws.cell(row=current_row, column=col, value=header)
                        cell.font = header_font
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                        cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
                    current_row += 1

                    # 参展人员数据
                    for participant in participants:
                        ws.cell(row=current_row, column=1, value=participant['name'] or '').font = cell_font
                        ws.cell(row=current_row, column=2, value=participant['phone'] or '').font = cell_font
                        ws.cell(row=current_row, column=3, value=participant['id_number'] or '').font = cell_font
                        ws.cell(row=current_row, column=4, value=participant['license_plate'] or '').font = cell_font
                        ws.cell(row=current_row, column=5, value=participant['role'] or '').font = cell_font
                        ws.cell(row=current_row, column=6, value='是' if participant['is_contact'] else '否').font = cell_font
                        current_row += 1

                    # 单位间空行
                    current_row += 1

                # 调整列宽
                column_widths = [15, 15, 20, 15, 15, 12]
                for col, width in enumerate(column_widths, 1):
                    ws.column_dimensions[get_column_letter(col)].width = width

                # 保存到内存
                print("开始保存Excel文件到内存")
                output = io.BytesIO()
                wb.save(output)
                output.seek(0)
                print(f"Excel文件保存成功，大小: {len(output.getvalue())} 字节")

                # 生成文件名
                filename = f"{exhibition['title']}_参展单位信息_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                print(f"生成文件名: {filename}")

                # 处理中文文件名编码
                import urllib.parse
                encoded_filename = urllib.parse.quote(filename.encode('utf-8'))

                # 返回文件
                response = make_response(output.getvalue())
                response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                # 使用RFC 5987标准处理中文文件名
                response.headers['Content-Disposition'] = f"attachment; filename*=UTF-8''{encoded_filename}"

                print("响应创建成功，准备返回文件")
                return response

        except Exception as e:
            print(f"导出过程中发生异常: {str(e)}")
            import traceback
            traceback.print_exc()  # 打印完整的错误堆栈
            return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})
        finally:
            if 'connection' in locals() and connection:
                connection.close()
                print("数据库连接已关闭")

    except Exception as e:
        print(f"导出过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()  # 打印完整的错误堆栈
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})







@teacher_bp.route('/get_teacher_info', methods=['GET'])
def get_teacher_info():
    """获取教师信息"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})
    
    user_id = session['user_id']
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT
                    u.user_id, u.name, u.phone_number, u.contact_info,
                    s.id AS school_id, s.name AS school_name
                FROM users u
                LEFT JOIN schools s ON u.teacher_school_id = s.id
                WHERE u.user_id = %s
            """
            cursor.execute(sql, (user_id,))
            teacher_info = cursor.fetchone()
            
            if not teacher_info:
                return jsonify({"code": 1, "message": "教师信息不存在"})
            
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": teacher_info
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取教师信息失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/edit_teacher_course', methods=['POST'])
def edit_teacher_course():
    """编辑主讲课程"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    teacher_id = session.get('user_id')
    course_id = request.form.get('id')
    course_name = request.form.get('course_name')
    semester = request.form.get('semester')
    course_type = request.form.get('course_type')
    student_count = request.form.get('student_count')
    
    if not all([course_id, course_name, semester, course_type, student_count]):
        return jsonify({"code": 1, "message": "请填写完整课程信息"})
    
    try:
        student_count = int(student_count)
        if student_count <= 0:
            return jsonify({"code": 1, "message": "开课人数必须为正整数"})
    except ValueError:
        return jsonify({"code": 1, "message": "开课人数必须为数字"})
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查课程是否存在且属于当前教师
            sql = "SELECT id FROM teacher_courses WHERE id = %s AND teacher_id = %s"
            cursor.execute(sql, (course_id, teacher_id))
            if not cursor.fetchone():
                return jsonify({"code": 1, "message": "课程不存在或无权编辑"})
            
            # 更新课程信息
            sql = """
                UPDATE teacher_courses 
                SET course_name = %s, semester = %s, course_type = %s, student_count = %s
                WHERE id = %s AND teacher_id = %s
            """
            cursor.execute(sql, (course_name, semester, course_type, student_count, course_id, teacher_id))
            connection.commit()
            
            return jsonify({"code": 0, "message": "课程更新成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"更新课程失败: {str(e)}"})
    finally:
        connection.close()

@teacher_bp.route('/edit_sample_request', methods=['POST'])
def edit_sample_request():
    """编辑样书申请"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "请先登录"})
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        order_number = data.get('order_number')
        publisher_id = data.get('publisher_id')
        address_id = data.get('address_id')
        books_data = data.get('books_data')

        if not order_number or not address_id or not books_data:
            return jsonify({"code": 1, "message": "参数不完整"})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 验证地址是否存在且属于当前教师
                sql_check_address = "SELECT address_id FROM shipping_addresses WHERE address_id = %s AND teacher_id = %s"
                cursor.execute(sql_check_address, (address_id, session['user_id']))
                if not cursor.fetchone():
                    return jsonify({"code": 1, "message": "收货地址无效或不属于您"})
                
                # 验证订单是否存在且属于当前教师
                sql_check_order = """
                    SELECT sr.request_id, sr.status 
                    FROM sample_requests sr
                    WHERE sr.order_number = %s AND sr.teacher_id = %s
                """
                cursor.execute(sql_check_order, (order_number, session['user_id']))
                request_results = cursor.fetchall()
                
                if not request_results:
                    return jsonify({"code": 1, "message": "订单不存在或不属于您"})
                
                # 检查是否为待审核状态
                if any(result['status'] != 'pending' for result in request_results):
                    return jsonify({"code": 1, "message": "只能编辑待审核状态的申请"})
                
                # 更新收货地址
                if publisher_id:
                    update_address_sql = """
                        UPDATE sample_requests sr
                        JOIN sample_books sb ON sr.textbook_id = sb.id
                        JOIN users u ON sb.publisher_id = u.user_id
                        SET sr.address_id = %s
                        WHERE sr.order_number = %s 
                        AND sr.teacher_id = %s
                        AND u.publisher_company_id = %s
                    """
                    params_for_update = [address_id, order_number, session['user_id'], publisher_id]
                else:
                    update_address_sql = """
                        UPDATE sample_requests sr
                        SET sr.address_id = %s
                        WHERE sr.order_number = %s 
                        AND sr.teacher_id = %s
                    """
                    params_for_update = [address_id, order_number, session['user_id']]
                
                cursor.execute(update_address_sql, params_for_update)
                
                # 逐个处理书籍数据
                success_count = 0
                
                # 收集要保留的request_id列表
                keep_request_ids = []
                for book_item in books_data:
                    request_id = book_item.get('request_id')
                    if request_id:
                        keep_request_ids.append(request_id)
                
                # 删除不在提交数据中的样书记录（用户从UI中删除的样书）
                if keep_request_ids:
                    # 这里需要修改使用的publisher_condition，因为删除语句中表名不同
                    delete_publisher_condition = ""
                    if publisher_id:
                        delete_publisher_condition = """
                            AND EXISTS (
                                SELECT 1
                                FROM sample_books sb
                                JOIN users u ON sb.publisher_id = u.user_id
                                WHERE sb.id = sample_requests.textbook_id
                                AND u.publisher_company_id = %s
                            )
                        """
                    
                    delete_removed_sql = f"""
                        DELETE FROM sample_requests
                        WHERE order_number = %s 
                        AND teacher_id = %s
                        AND status = 'pending'
                        AND request_id NOT IN ({','.join(['%s'] * len(keep_request_ids))})
                        {delete_publisher_condition}
                    """
                    # 构建参数列表：订单号、教师ID、所有要保留的request_ids、可能的publisher_id
                    delete_params = [order_number, session['user_id']] + keep_request_ids
                    if publisher_id:
                        delete_params.append(publisher_id)
                    
                    cursor.execute(delete_removed_sql, delete_params)
                    removed_count = cursor.rowcount
                else:
                    # 如果没有要保留的样书（全部删除），删除整个订单的对应出版社的申请
                    # 这里也需要修改使用的publisher_condition
                    delete_publisher_condition = ""
                    if publisher_id:
                        delete_publisher_condition = """
                            AND EXISTS (
                                SELECT 1
                                FROM sample_books sb
                                JOIN users u ON sb.publisher_id = u.user_id
                                WHERE sb.id = sample_requests.textbook_id
                                AND u.publisher_company_id = %s
                            )
                        """
                    
                    delete_all_sql = f"""
                        DELETE FROM sample_requests
                        WHERE order_number = %s 
                        AND teacher_id = %s
                        AND status = 'pending'
                        {delete_publisher_condition}
                    """
                    delete_all_params = [order_number, session['user_id']]
                    if publisher_id:
                        delete_all_params.append(publisher_id)
                        
                    cursor.execute(delete_all_sql, delete_all_params)
                    removed_count = cursor.rowcount
                
                for book_item in books_data:
                    request_id = book_item.get('request_id')
                    course_id = book_item.get('course_id')
                    quantity = book_item.get('quantity', 1)
                    purpose = book_item.get('purpose', '教材')
                    item_remark = book_item.get('item_remark', '')
                    
                    if not request_id or not course_id:
                        continue
                    
                    # 验证课程是否存在且属于当前用户
                    sql_check_course = "SELECT id FROM teacher_courses WHERE id = %s AND teacher_id = %s"
                    cursor.execute(sql_check_course, (course_id, session['user_id']))
                    if not cursor.fetchone():
                        continue
                    
                    # 验证申请条目是否存在且属于当前教师
                    sql_check_request = """
                        SELECT request_id 
                        FROM sample_requests 
                        WHERE request_id = %s AND teacher_id = %s AND status = 'pending'
                    """
                    cursor.execute(sql_check_request, (request_id, session['user_id']))
                    if not cursor.fetchone():
                        continue
                    
                    # 更新申请条目
                    sql_update_request = """
                        UPDATE sample_requests 
                        SET course_id = %s, quantity = %s, purpose = %s, request_reason = %s
                        WHERE request_id = %s AND teacher_id = %s AND status = 'pending'
                    """
                    cursor.execute(sql_update_request, (
                        course_id, quantity, purpose, item_remark, request_id, session['user_id']
                    ))
                    success_count += 1
                
                connection.commit()
                
                return jsonify({
                    "code": 0, 
                    "message": f"申请修改成功，共更新 {success_count} 本样书信息",
                    "success_count": success_count
                })

        except Exception as e:
            if connection:
                connection.rollback()
            return jsonify({"code": 1, "message": f"编辑样书申请失败: {str(e)}"})
        finally:
            if connection:
                connection.close()
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"请求处理错误: {str(e)}"})

@teacher_bp.route('/get_address_data', methods=['GET'])
def get_address_data():
    """获取三级联动地址数据"""
    try:
        # 读取地址数据文件
        import os
        import json
        from flask import current_app
        
        # 构建文件路径
        address_file_path = os.path.join(current_app.root_path, 'static', 'level3_address.json')
        
        # 检查文件是否存在
        if not os.path.exists(address_file_path):
            return jsonify({
                "code": 1, 
                "message": f"地址数据文件不存在: {address_file_path}"
            })
        
        # 读取文件
        with open(address_file_path, 'r', encoding='utf-8-sig') as f:
            address_data = json.load(f)
        
        # 添加调试信息
        print(f"成功加载地址数据，省份数量: {len(address_data)}")
        
        return jsonify({
            "code": 0,
            "message": "获取地址数据成功",
            "data": address_data
        })
    except FileNotFoundError:
        return jsonify({"code": 1, "message": "地址数据文件不存在"})
    except json.JSONDecodeError as e:
        return jsonify({"code": 1, "message": f"地址数据格式错误: {str(e)}"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取地址数据失败: {str(e)}"})

@teacher_bp.route('/get_address_detail', methods=['GET'])
def get_address_detail():
    """获取单个地址详情"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    address_id = request.args.get('address_id')
    if not address_id:
        return jsonify({"code": 1, "message": "地址ID不能为空"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT address_id, name, phone_number, province, city, district, detailed_address
                FROM shipping_addresses
                WHERE address_id = %s AND teacher_id = %s
            """
            cursor.execute(sql, (address_id, session['user_id']))
            address = cursor.fetchone()
            
            if address:
                return jsonify({
                    "code": 0,
                    "message": "获取地址详情成功",
                    "data": address
                })
            else:
                return jsonify({"code": 1, "message": "地址不存在或无权限访问"})
    except Exception as e:
        return jsonify({"code": 1, "message": "获取地址详情失败"})
    finally:
        connection.close()

@teacher_bp.route('/search_co_organizers', methods=['GET'])
def search_co_organizers():
    """搜索协办方组织"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    query = request.args.get('query', '').strip()
    if not query:
        return jsonify({"code": 1, "message": "搜索关键词不能为空"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            organizers = []

            # 搜索出版社组织
            cursor.execute("""
                SELECT id, name, 'publisher' as type
                FROM publisher_companies
                WHERE name LIKE %s
                ORDER BY name
                LIMIT 10
            """, (f'%{query}%',))

            publishers = cursor.fetchall()
            for pub in publishers:
                organizers.append({
                    'id': pub['id'],
                    'name': pub['name'],
                    'type': pub['type']
                })

            # 搜索经销商组织
            cursor.execute("""
                SELECT id, name, 'dealer' as type
                FROM dealer_companies
                WHERE name LIKE %s
                ORDER BY name
                LIMIT 10
            """, (f'%{query}%',))

            dealers = cursor.fetchall()
            for dealer in dealers:
                organizers.append({
                    'id': dealer['id'],
                    'name': dealer['name'],
                    'type': dealer['type']
                })

            # 按名称排序
            organizers.sort(key=lambda x: x['name'])

            return jsonify({
                "code": 0,
                "message": "搜索成功",
                "data": organizers[:20]  # 最多返回20个结果
            })
    except Exception as e:
        return jsonify({"code": 1, "message": f"搜索失败: {str(e)}"})
    finally:
        connection.close()