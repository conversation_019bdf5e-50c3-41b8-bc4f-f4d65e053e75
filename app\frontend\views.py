from flask import Blueprint, render_template, redirect, url_for, session, request, jsonify,send_file
import pymysql.cursors
from app.config import get_db_connection

frontend_bp = Blueprint('frontend', __name__)

@frontend_bp.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('frontend.dashboard'))
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/login')
def login():
    """
    登录页面路由
    支持通过url参数指定登录入口，根据数据库配置返回对应模板
    """
    # 获取URL参数
    login_url = request.args.get('url')

    # 如果没有指定url参数，返回404
    if not login_url:
        return render_template('404.html'), 404

    try:
        # 查询数据库中匹配的login_url
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查找匹配的启用状态的网站设置
        sql = """
            SELECT user_role
            FROM site_settings
            WHERE login_url = %s AND is_active = 1
        """
        cursor.execute(sql, (login_url,))
        result = cursor.fetchone()

        cursor.close()
        connection.close()

        # 如果没有找到匹配的配置，返回404
        if not result:
            return render_template('404.html'), 404

        # 根据角色返回对应的模板
        user_role = result['user_role']

        if user_role in ['teacher', 'admin']:
            return render_template('login_teacher.html')
        elif user_role == 'publisher':
            return render_template('login_publisher.html')
        elif user_role == 'dealer':
            return render_template('login_dealer.html')
        else:
            # 如果是其他角色（如default），返回404
            return render_template('404.html'), 404

    except Exception as e:
        # 发生错误时返回404页面
        print(f"登录路由错误: {str(e)}")
        return render_template('404.html'), 404

@frontend_bp.route('/register')
def register():
    return render_template('register.html')

@frontend_bp.route('/forgot_password')
def forgot_password():
    return render_template('forgot_password.html')

@frontend_bp.route('/dashboard')
def dashboard():
    if 'username' in session:
        user_role = session.get('role')
        return render_template('dashboard.html', user_role=user_role)
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/edit_profile')
def edit_profile():
    if 'user_id' in session:
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 获取用户基本信息
                sql = "SELECT u.user_id, u.username, u.name, u.phone_number, u.email, u.role, \
                      u.teacher_school_id, u.publisher_company_id, u.dealer_company_id \
                      FROM users u WHERE u.user_id = %s"
                cursor.execute(sql, (session['user_id'],))
                user = cursor.fetchone()
                
                if not user:
                    return redirect(url_for('frontend.login'))
                
                # 根据角色获取所属单位信息
                organization_name = None
                if user['role'] == 'teacher' and user['teacher_school_id']:
                    cursor.execute("SELECT name FROM schools WHERE id = %s", (user['teacher_school_id'],))
                    school = cursor.fetchone()
                    if school:
                        organization_name = school['name']
                
                elif user['role'] == 'publisher' and user['publisher_company_id']:
                    cursor.execute("SELECT name FROM publisher_companies WHERE id = %s", (user['publisher_company_id'],))
                    company = cursor.fetchone()
                    if company:
                        organization_name = company['name']
                
                elif user['role'] == 'dealer' and user['dealer_company_id']:
                    cursor.execute("SELECT name FROM dealer_companies WHERE id = %s", (user['dealer_company_id'],))
                    company = cursor.fetchone()
                    if company:
                        organization_name = company['name']
                
                user['organization_name'] = organization_name
                
                return render_template('edit_profile.html', user=user)
        finally:
            connection.close()
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/manage_samples')
def manage_samples():
    if 'username' in session:
        return render_template('manage_samples.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/upload_sample')
def upload_sample():
    if 'username' in session:
        return render_template('upload_sample.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/edit_sample')
def edit_sample():
    if 'username' in session:
        return render_template('edit_sample.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/request_samples', methods=['GET'])
def request_samples():
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询所有样书信息
            sql = "SELECT id, name, author, price, award_info FROM sample_books"
            cursor.execute(sql)
            books = cursor.fetchall()
            return render_template('request_samples.html', books=books)
    finally:
        connection.close()

@frontend_bp.route('/manage_addresses')
def manage_addresses():
    if 'username' in session:
        return render_template('manage_addresses.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/manage_sample_requests')
def manage_sample_requests():
    if 'username' in session:
        return render_template('manage_sample_requests.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/teacher_manage_sample_requests')
def teacher_manage_sample_requests():
    if 'username' in session:
        return render_template('teacher_manage_sample_requests.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/manage_users')
def manage_users():
    if 'username' in session and session.get('role') == 'admin':
        return render_template('manage_users.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/admin_manage_samples')
def admin_manage_samples():
    if 'username' in session and session.get('role') == 'admin':
        return render_template('admin_manage_samples.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/admin_manage_sample_requests')
def admin_manage_sample_requests():
    if 'username' in session and session.get('role') == 'admin':
        return render_template('pc_admin_manage_sample_requests.html')
    else:
        return redirect(url_for('frontend.login'))

# @frontend_bp.route('/admin/dashboard', methods=['GET'])
# def admin_dashboard():
#     if 'username' in session and session.get('role') == 'admin':
#         connection = get_db_connection()
#         try:
#             with connection.cursor() as cursor:
#                 # 获取用户数量
#                 cursor.execute("SELECT COUNT(*) AS user_count FROM users")
#                 user_count = cursor.fetchone()['user_count']

#                 # 获取教师数量
#                 cursor.execute("SELECT COUNT(*) AS teacher_count FROM users WHERE role = 'teacher'")
#                 teacher_count = cursor.fetchone()['teacher_count']

#                 # 获取出版社数量
#                 cursor.execute("SELECT COUNT(*) AS publisher_count FROM users WHERE role = 'publisher'")
#                 publisher_count = cursor.fetchone()['publisher_count']

#                 # 获取已审核申请数量
#                 cursor.execute("SELECT COUNT(*) AS approved_count FROM sample_requests WHERE status != 'pending'")
#                 approved_count = cursor.fetchone()['approved_count']

#                 # 获取未审核申请数量
#                 cursor.execute("SELECT COUNT(*) AS pending_count FROM sample_requests WHERE status = 'pending'")
#                 pending_count = cursor.fetchone()['pending_count']

#                 # 获取样书总数
#                 cursor.execute("SELECT COUNT(*) AS book_count FROM sample_books")
#                 book_count = cursor.fetchone()['book_count']

#                 # 获取合作样书数量
#                 cursor.execute("SELECT COUNT(*) AS cooperative_books FROM sample_books WHERE cooperation_status = 1")
#                 cooperative_books = cursor.fetchone()['cooperative_books']

#                 # 获取样书申请总数
#                 cursor.execute("SELECT COUNT(*) AS total_requests FROM sample_requests")
#                 total_requests = cursor.fetchone()['total_requests']

#                 return render_template('admin_dashboard.html', 
#                                        user_count=user_count, 
#                                        teacher_count=teacher_count, 
#                                        publisher_count=publisher_count, 
#                                        approved_count=approved_count, 
#                                        pending_count=pending_count, 
#                                        book_count=book_count, 
#                                        cooperative_books=cooperative_books, 
#                                        total_requests=total_requests)
#         finally:
#             connection.close()
#     else:
#         return redirect(url_for('frontend.login'))

@frontend_bp.route('/report_samples')
def report_samples():
    if 'username' in session:# and session.get('role') == 'dealer'
        return render_template('report_samples.html')
    else:
        return redirect(url_for('frontend.login'))

@frontend_bp.route('/manage_reports')
def manage_reports_page():
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('manage_reports.html')

@frontend_bp.route('/manage_report_requests')
def manage_report_requests_page():
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('manage_report_requests.html') 

@frontend_bp.route('/admin_manage_report_requests')
def admin_manage_report_requests_page():
    if session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('admin_manage_reports.html')

@frontend_bp.route('/mobile_dashboard')
def mobile_dashboard():
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('mobile_dashboard.html')

@frontend_bp.route('/mobile_publisher_manage_samples')
def mobile_publisher_manage_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_publisher_manage_samples.html')

@frontend_bp.route('/mobile_publisher_manage_sample_requests')
def mobile_publisher_manage_sample_requests():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_publisher_manage_sample_requests.html')

@frontend_bp.route('/mobile_publisher_manage_report_requests')
def mobile_publisher_manage_report_requests():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_publisher_manage_report_requests.html')

@frontend_bp.route('/mobile_admin_manage_samples')
def mobile_admin_manage_samples():
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_admin_manage_samples.html')

@frontend_bp.route('/mobile_admin_manage_report_requests')
def mobile_admin_manage_report_requests():
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_admin_manage_report_requests.html')

@frontend_bp.route('/mobile_teacher_sample_requests')
def mobile_teacher_sample_requests():
    if 'user_id' not in session or session.get('role') != 'teacher':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_teacher_sample_requests.html')

@frontend_bp.route('/mobile_teacher_manage_addresses')
def mobile_teacher_manage_addresses():
    if 'user_id' not in session or session.get('role') != 'teacher':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_teacher_manage_addresses.html')

@frontend_bp.route('/mobile_dealer_report_requests')
def mobile_dealer_report_requests():
    if 'user_id' not in session or session.get('role') != 'dealer':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_dealer_report_requests.html')

@frontend_bp.route('/mobile_admin_manage_users')
def mobile_admin_manage_users():
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_admin_manage_users.html')

@frontend_bp.route('/mobile_edit_profile')
def mobile_edit_profile():
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('mobile_edit_profile.html')

@frontend_bp.route('/mobile_admin_dashboard')
def mobile_admin_dashboard():
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('mobile_admin_dashboard.html')

@frontend_bp.route('/admin/dashboard')
def admin_dashboard():
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('admin_dashboard.html')

@frontend_bp.route('/pc_teacher_request_samples')
def pc_teacher_request_sample():
    if 'user_id' not in session or session.get('role') != 'teacher':
        return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_request_samples.html')

@frontend_bp.route('/pc_teacher_manage_requests')
def pc_teacher_manage_requests():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_manage_requests.html')

@frontend_bp.route('/pc_publisher_manage_sample_requests')
def pc_publisher_manage_sample_requests():
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_manage_sample_requests.html')

@frontend_bp.route('/pc_publisher_manage_samples')
def pc_publisher_manage_samples():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_manage_samples.html')

@frontend_bp.route('/pc_publisher_rates')
def pc_publisher_rates():
    if 'user_id' not in session or session.get('role') != 'publisher':
        return redirect(url_for('frontend.login'))

    # 检查用户是否有查看费率信息的权限
    from app.users.publisher import check_user_permission
    if not check_user_permission(session['user_id'], 'view_rate_info'):
        return redirect(url_for('frontend.pc_publisher_manage_samples'))

    return render_template('pc_publisher_rates.html')

@frontend_bp.route('/pc_dealer_report_samples')
def pc_dealer_report_samples():
    # if 'user_id' not in session or session.get('role') != 'dealer':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_dealer_report_samples.html')

@frontend_bp.route('/pc_dealer_manage_reports')
def pc_dealer_manage_reports():
    # if 'user_id' not in session or session.get('role') != 'dealer':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_dealer_manage_reports.html')

@frontend_bp.route('/pc_publisher_manage_report_requests')
def pc_publisher_manage_report_requests():
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_manage_report_requests.html')

@frontend_bp.route('/pc_publisher_manage_orders')
def pc_publisher_manage_orders():
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_manage_orders.html')

@frontend_bp.route('/pc_dealer_manage_orders')
def pc_dealer_manage_orders():
    # if 'user_id' not in session or session.get('role') != 'dealer':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_dealer_manage_orders.html')

@frontend_bp.route('/pc_teacher_manage_exhibitions')
def pc_teacher_manage_exhibitions():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_manage_exhibitions.html')

@frontend_bp.route('/teacher/exhibitions')
def teacher_exhibitions():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_manage_exhibitions.html')

@frontend_bp.route('/teacher/exhibitions/create')
def teacher_create_exhibition():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_create_exhibition.html')

@frontend_bp.route('/teacher/exhibitions/edit')
def teacher_edit_exhibition():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_create_exhibition.html')

@frontend_bp.route('/teacher/exhibitions/detail')
def teacher_exhibition_detail():
    # if 'user_id' not in session or session.get('role') != 'teacher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_teacher_exhibition_detail.html')

@frontend_bp.route('/pc_publisher_manage_exhibitions')
def pc_publisher_manage_exhibitions():
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_manage_exhibitions.html')

@frontend_bp.route('/publisher/exhibitions/<int:exhibition_id>')
def pc_publisher_exhibition_detail(exhibition_id):
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_exhibition_detail.html')

@frontend_bp.route('/publisher/exhibitions/<int:exhibition_id>/registration')
def pc_publisher_exhibition_registration(exhibition_id):
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_exhibition_registration.html')

@frontend_bp.route('/pc_admin_manage_exhibitions', methods=['GET'])
def pc_admin_manage_exhibitions_page():
    """管理员管理书展活动页面"""
    # if 'user_id' not in session or session.get('role') != 'admin':
    #     return jsonify({"code": 1, "message": "无权限访问"})

    return render_template('pc_admin_manage_exhibitions.html')

@frontend_bp.route('/co_organizer_exhibitions')
def co_organizer_exhibitions():
    """协办方书展审核页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))

    user_role = session.get('role')
    if user_role not in ['publisher', 'dealer']:
        return redirect(url_for('frontend.dashboard'))

    return render_template('pc_co_organizer_exhibitions.html')

@frontend_bp.route('/exhibition_review_history')
def exhibition_review_history():
    """书展审核历史页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))

    return render_template('exhibition_review_history.html')

@frontend_bp.route('/pc_admin_manage_users', methods=['GET'])
def pc_admin_manage_users():
    """
    渲染用户管理页面
    """
    # if 'user_id' not in session or session.get('role') != 'admin':
    #     return jsonify({"code": 1, "message": "无权限访问"})

    return render_template('pc_admin_manage_users.html')

@frontend_bp.route('/pc_admin_manage_account_bindings', methods=['GET'])
def pc_admin_manage_account_bindings():
    """
    渲染账号绑定管理页面
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')

    return render_template('pc_admin_manage_account_bindings.html')

@frontend_bp.route('/pc_admin_manage_organizations', methods=['GET'])
def pc_admin_manage_organizations():
    """管理员管理组织页面"""
    # if 'user_id' not in session or session.get('role') != 'admin':
    #     return jsonify({"code": 1, "message": "无权限访问"})
    return render_template('pc_admin_manage_organizations.html')

@frontend_bp.route('/pc_admin_email_configs', methods=['GET'])
def pc_admin_email_configs():
    """管理员邮件配置管理页面"""
    # if 'user_id' not in session or session.get('role') != 'admin':
    #     return jsonify({"code": 1, "message": "无权限访问"})
    return render_template('pc_admin_email_configs.html')

@frontend_bp.route('/pc_admin_site_settings', methods=['GET'])
def pc_admin_site_settings():
    """管理员网站设置管理页面"""
    # if 'user_id' not in session or session.get('role') != 'admin':
    #     return jsonify({"code": 1, "message": "无权限访问"})
    return render_template('pc_admin_site_settings.html')

@frontend_bp.route('/pc_admin_manage_user_permissions', methods=['GET'])
def pc_admin_manage_user_permissions():
    """管理员用户权限管理页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect(url_for('frontend.login'))
    return render_template('pc_admin_manage_user_permissions.html')

@frontend_bp.route('/pc_dealer_book_recommendations', methods=['GET'])
def pc_dealer_book_recommendations():
    # if 'user_id' not in session or session.get('role') != 'dealer':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_dealer_book_recommendations.html')

@frontend_bp.route('/pc_publisher_book_recommendations', methods=['GET'])
def pc_publisher_book_recommendations():
    # if 'user_id' not in session or session.get('role') != 'publisher':
    #     return redirect(url_for('frontend.login'))
    return render_template('pc_publisher_book_recommendations.html')

@frontend_bp.route('/common/book_selector', methods=['GET'])
def book_selector():
    return render_template('common_book_selector.html')

# @frontend_bp.route('/<path:filename>')
# def pki_validation(filename):
#     directory='/www/wwwroot/managebooks/downloads/'
#     path = directory + filename
#     return send_file(path,as_attachment=True)

@frontend_bp.route('/pc_admin_manage_sample_fields')
def manage_sample_fields():
    """管理员样书字段管理页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')
    return render_template('pc_admin_manage_sample_fields.html')

@frontend_bp.route('/pc_admin_manage_book_recommendations')
def pc_admin_manage_book_recommendations():
    """管理员换版推荐管理页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')
    return render_template('pc_admin_manage_book_recommendations.html')

@frontend_bp.route('/pc_admin_manage_audit_logs')
def pc_admin_manage_audit_logs():
    """管理员审计日志查看页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')
    return render_template('pc_admin_manage_audit_logs.html')

@frontend_bp.route('/admin_price_changes')
def admin_price_changes():
    """管理员价格变动管理页面"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return redirect('/login')
    return render_template('admin_price_changes.html')

@frontend_bp.route('/my-shared-lists')
def my_shared_lists():
    """我的分享清单管理页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('my_shared_lists.html')

@frontend_bp.route('/shared-lists/create')
def create_shared_list():
    """创建分享清单页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))

    # 获取样书ID列表参数
    book_ids = request.args.get('book_ids', '')
    return render_template('create_shared_list.html', book_ids=book_ids)

@frontend_bp.route('/shared-lists/<int:list_id>/edit')
def edit_shared_list(list_id):
    """编辑分享清单页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('edit_shared_list.html', list_id=list_id)

@frontend_bp.route('/shared/<share_token>')
def public_shared_list(share_token):
    """公开分享清单详情页面（无需登录）"""
    return render_template('public_shared_list.html', share_token=share_token)

@frontend_bp.route('/public/shared-lists/<share_token>')
def public_shared_list_alt(share_token):
    """公开分享清单详情页面的备用路由（无需登录）"""
    return render_template('public_shared_list.html', share_token=share_token)

@frontend_bp.route('/shared-lists/<int:list_id>/stats')
def shared_list_stats(list_id):
    """分享清单统计分析页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('shared_list_stats.html', list_id=list_id)

@frontend_bp.route('/shared-lists/<int:list_id>/visits')
def shared_list_visits(list_id):
    """分享清单访问记录详情页面"""
    if 'user_id' not in session:
        return redirect(url_for('frontend.login'))
    return render_template('shared_list_visits.html', list_id=list_id)