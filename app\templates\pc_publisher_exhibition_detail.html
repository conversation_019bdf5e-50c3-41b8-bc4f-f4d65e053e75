<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材巡展详情 - 供应商中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 自定义样式 */
        .detail-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }
        
        .info-item {
            transition: all 0.2s ease;
        }
        
        .info-item:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .participant-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }
        
        .participant-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .form-input {
            transition: all 0.2s ease;
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 表单验证样式 */
        .form-input.border-red-300:focus {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.border-yellow-300:focus {
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }

        /* 必填项标识 */
        .required-field::after {
            content: " *";
            color: #ef4444;
        }

        /* 联系人选择区域 */
        .contact-selection {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #bfdbfe;
        }

        .contact-selection.selected {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
        }

        /* 富文本内容样式 */
        #exhibitionDescription {
            line-height: 1.6;
        }

        #exhibitionDescription img {
            width: 33%;
            height: auto;
            display: block;
            margin: 0.5em auto;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #exhibitionDescription h1 {
            font-size: 2rem;
            font-weight: 700;
            margin: 1.5rem 0 1rem 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }

        #exhibitionDescription h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 1.25rem 0 0.75rem 0;
            color: #374151;
        }

        #exhibitionDescription h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 1rem 0 0.5rem 0;
            color: #4b5563;
        }

        #exhibitionDescription h4 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0.875rem 0 0.5rem 0;
            color: #6b7280;
        }

        #exhibitionDescription h5 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #6b7280;
        }

        #exhibitionDescription h6 {
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #9ca3af;
        }

        #exhibitionDescription p {
            margin: 0.75rem 0;
            color: #374151;
        }

        #exhibitionDescription ul, #exhibitionDescription ol {
            margin: 0.75rem 0;
            padding-left: 1.5rem;
            color: #374151;
        }

        #exhibitionDescription li {
            margin: 0.25rem 0;
        }

        #exhibitionDescription strong {
            font-weight: 600;
            color: #1f2937;
        }

        #exhibitionDescription em {
            font-style: italic;
            color: #4b5563;
        }

        #exhibitionDescription a {
            color: #3b82f6;
            text-decoration: underline;
        }

        #exhibitionDescription a:hover {
            color: #2563eb;
        }

        #exhibitionDescription blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #6b7280;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>
    
    <!-- 返回按钮 -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button onclick="goBackToList()" class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                <span>返回列表</span>
            </button>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 编辑模式提示条 -->
        <div id="editModeAlert" class="mb-6 p-4 bg-orange-100 border-l-4 border-orange-500 rounded-lg hidden">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-edit text-orange-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-orange-700">
                        <strong>编辑模式：</strong>您正在编辑报名信息。点击"修改报名信息"开始编辑，点击"查看报名详情"查看当前报名，或点击"取消编辑"返回正常模式。
                    </p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="cancelEdit()" class="text-orange-600 hover:text-orange-800">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-spinner fa-spin text-xl text-blue-600"></i>
            </div>
            <p class="text-gray-600">加载中...</p>
        </div>

        <!-- 主要内容区域 -->
        <div id="mainContent" class="hidden">
            <!-- 头部信息卡片 -->
            <div class="detail-card rounded-2xl p-8 mb-8">
                <div class="flex flex-col">
                    <!-- 第一行：标题和状态标签 -->
                    <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                        <div class="flex-1">
                            <h1 id="exhibitionTitle" class="text-3xl font-bold text-gray-900 mb-4"></h1>
                            <div class="flex flex-wrap items-center gap-4">
                                <div id="statusBadges" class="flex gap-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行：报名状态独立一行 -->
                    <div id="registrationStatusRow" class="mb-6 p-4 rounded-lg border border-gray-200 bg-gray-50">
                        <!-- 报名状态内容将动态加载 -->
                    </div>

                    <!-- 第三行：展览基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-university text-blue-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">发起单位</span>
                            </div>
                            <p id="schoolName" class="text-gray-900 font-medium"></p>
                        </div>
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-calendar-alt text-green-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">开始时间</span>
                            </div>
                            <p id="startTime" class="text-gray-900 font-medium"></p>
                        </div>
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-calendar-check text-red-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">结束时间</span>
                            </div>
                            <p id="endTime" class="text-gray-900 font-medium"></p>
                        </div>
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-clock text-orange-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">报名截止</span>
                            </div>
                            <p id="registrationDeadline" class="text-gray-900 font-medium"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="space-y-6">
                <!-- 活动描述 -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                        活动详情
                    </h2>
                    <div id="exhibitionDescription" class="prose prose-gray max-w-none">
                        <!-- 活动描述内容 -->
                    </div>
                </div>

                <!-- 联系信息 -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-address-card text-green-600 mr-2"></i>
                        联系信息
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-user text-gray-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">联系人</span>
                            </div>
                            <p id="contactName" class="text-gray-900 font-medium"></p>
                        </div>
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-phone text-gray-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">联系电话</span>
                            </div>
                            <p id="contactPhone" class="text-gray-900 font-medium"></p>
                        </div>
                        <div class="info-item p-4 rounded-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-map-marker-alt text-gray-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-500">活动地点</span>
                            </div>
                            <p id="location" class="text-gray-900 font-medium"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报名表单模态框 -->
    <div id="registrationModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 id="modalTitle" class="text-xl font-semibold text-gray-900">报名参展</h3>
                <button onclick="closeRegistrationModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[70vh]">
                <!-- 填写说明 -->
                <div class="registration-instructions">
                    <!-- 说明内容将通过JavaScript动态更新 -->
                </div>

                <!-- 无车参展选择（仅在必填车牌时显示） -->
                <div id="noCarSection" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg" style="display: none;">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="noCarCheckbox" class="mr-3 text-blue-600 focus:ring-blue-500 w-4 h-4" onchange="toggleAllCarInputs()">
                        <span class="text-sm font-medium text-blue-700">
                            <i class="fas fa-walking mr-2"></i>整个团队无车参展
                        </span>
                    </label>
                    <p class="text-xs text-blue-600 mt-2 ml-7">
                        <i class="fas fa-info-circle mr-1"></i>
                        勾选后将禁用所有参展人员的车牌号输入
                    </p>
                </div>

                <form id="registrationForm" class="space-y-6">
                    <div id="participantsList" class="space-y-4">
                        <!-- 参展人员列表将动态生成 -->
                    </div>
                    <div class="text-center">
                        <button type="button" onclick="addParticipant()" class="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            添加参展人员
                        </button>
                        <p class="text-xs text-gray-500 mt-2" id="addParticipantTip">
                            <i class="fas fa-lightbulb mr-1"></i>
                            <!-- 提示内容将通过JavaScript动态更新 -->
                        </p>
                    </div>
                </form>
            </div>
            <div class="flex justify-end gap-3 p-6 border-t border-gray-200">
                <button onclick="closeRegistrationModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    取消
                </button>
                <button onclick="submitRegistration()" id="submitButton" class="btn-primary px-6 py-2 text-white rounded-lg">
                    提交报名
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let exhibitionId = null;
        let exhibitionData = null;
        let participantCount = 1;
        let licensePlateRequired = false; // 是否必须填写车牌号
        let isEditMode = false; // 是否为编辑模式
        let existingRegistrationData = null; // 现有报名数据
        
        // 页面加载完成后执行
        $(document).ready(function() {
            // 从URL获取展览ID
            const pathParts = window.location.pathname.split('/');
            exhibitionId = pathParts[pathParts.length - 1];

            // 检查是否为编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            isEditMode = urlParams.get('edit') === 'true';

            if (exhibitionId) {
                loadExhibitionDetail();
            } else {
                showError('无效的展览ID');
            }
        });
        
        // 加载展览详情
        function loadExhibitionDetail() {
            $.ajax({
                url: `/api/common/get_exhibition_detail/${exhibitionId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        exhibitionData = response.data;
                        licensePlateRequired = exhibitionData.license_plate_required || false;
                        renderExhibitionDetail(exhibitionData);

                        // 如果是编辑模式，加载现有报名数据
                        if (isEditMode) {
                            loadExistingRegistrationData();
                        } else {
                            $('#loadingState').addClass('hidden');
                            $('#mainContent').removeClass('hidden');
                        }
                    } else {
                        showError(response.message || '加载失败');
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 加载现有报名数据
        function loadExistingRegistrationData() {
            $.ajax({
                url: `/api/common/get_registration_detail?exhibition_id=${exhibitionId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        existingRegistrationData = response.data;
                        console.log('Existing registration data:', existingRegistrationData);

                        // 显示编辑提示并等待用户操作
                        showMessage('已加载现有报名数据，请点击"修改报名信息"按钮开始编辑', 'info');
                    } else {
                        showMessage('无法加载现有报名数据：' + (response.message || '未知错误'), 'error');
                        // 编辑模式失败，退回到正常模式
                        isEditMode = false;
                        const url = new URL(window.location);
                        url.searchParams.delete('edit');
                        window.history.replaceState({}, '', url.toString());
                    }

                    $('#loadingState').addClass('hidden');
                    $('#mainContent').removeClass('hidden');
                },
                error: function() {
                    showMessage('加载现有报名数据失败', 'error');
                    // 编辑模式失败，退回到正常模式
                    isEditMode = false;
                    const url = new URL(window.location);
                    url.searchParams.delete('edit');
                    window.history.replaceState({}, '', url.toString());

                    $('#loadingState').addClass('hidden');
                    $('#mainContent').removeClass('hidden');
                }
            });
        }

        // 填充现有数据到表单
        function fillExistingData() {
            if (!existingRegistrationData || !existingRegistrationData.participants) return;

            const participants = existingRegistrationData.participants;
            participantCount = participants.length;

            // 检查是否整个团队无车
            const hasAnyLicensePlate = participants.some(p => p.license_plate);
            if (!hasAnyLicensePlate && licensePlateRequired) {
                $('#noCarCheckbox').prop('checked', true);
            }

            // 重新渲染参展人员列表
            renderParticipantsList();

            // 填充每个参展人员的数据
            participants.forEach((participant, index) => {
                const i = index + 1;
                $(`input[name="name_${i}"]`).val(participant.name || '');
                $(`input[name="phone_${i}"]`).val(participant.phone || '');
                $(`input[name="role_${i}"]`).val(participant.role || '');
                $(`input[name="license_plate_${i}"]`).val(participant.license_plate || '');

                // 设置联系人
                if (participant.is_contact) {
                    $(`input[name="contact_person"][value="${i}"]`).prop('checked', true);
                    updateContactSelection(i);
                }
            });

            // 如果是无车状态，禁用车牌输入
            if (!hasAnyLicensePlate && licensePlateRequired) {
                toggleAllCarInputs();
            }
        }

        // 渲染展览详情
        function renderExhibitionDetail(exhibition) {
            // 设置基本信息
            $('#exhibitionTitle').text(exhibition.title);
            $('#schoolName').text(exhibition.school_name);
            $('#startTime').text(exhibition.start_time);
            $('#endTime').text(exhibition.end_time);
            $('#registrationDeadline').text(exhibition.registration_deadline);
            $('#contactName').text(exhibition.contact_name);
            $('#contactPhone').text(exhibition.contact_phone || '未提供');
            $('#location').text(exhibition.location);

            // 设置活动描述
            $('#exhibitionDescription').html(exhibition.description || '<p class="text-gray-500">暂无详细描述</p>');

            // 显示或隐藏编辑模式提示条
            if (isEditMode) {
                $('#editModeAlert').removeClass('hidden');
            } else {
                $('#editModeAlert').addClass('hidden');
            }

            // 渲染状态标签
            renderStatusBadges(exhibition);

            // 渲染报名状态行
            renderRegistrationStatusRow(exhibition);
        }

        // 渲染状态标签
        function renderStatusBadges(exhibition) {
            const statusInfo = getExhibitionStatusInfo(exhibition);
            let badgesHtml = `<span class="status-badge ${statusInfo.main.class}">${statusInfo.main.text}</span>`;

            if (statusInfo.secondary) {
                badgesHtml += `<span class="status-badge ${statusInfo.secondary.class}">${statusInfo.secondary.text}</span>`;
            }

            $('#statusBadges').html(badgesHtml);
        }

        // 渲染报名状态行（新增函数）
        function renderRegistrationStatusRow(exhibition) {
            let statusRowHtml = '';

            if (isEditMode) {
                // 编辑模式
                statusRowHtml = `
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex items-center mb-3 sm:mb-0">
                            <i class="fas fa-edit text-orange-600 mr-2"></i>
                            <span class="text-lg font-semibold text-orange-800">正在编辑报名信息</span>
                            <span class="ml-2 text-sm text-orange-600">修改完成后请重新提交</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button onclick="showRegistrationModal()" class="px-4 py-2 btn-primary text-white rounded-lg text-sm">
                                <i class="fas fa-edit mr-1"></i>修改报名信息
                            </button>
                            <button onclick="viewRegistrationDetail()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                                <i class="fas fa-eye mr-1"></i>查看报名详情
                            </button>
                            <button onclick="cancelEdit()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                                <i class="fas fa-times mr-1"></i>取消编辑
                            </button>
                        </div>
                    </div>
                `;
            } else if (exhibition.is_registered) {
                statusRowHtml = `
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex items-center mb-3 sm:mb-0">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <span class="text-lg font-semibold text-green-800">您已成功报名此次活动</span>
                            <span class="ml-2 text-sm text-green-600">请按时参加活动</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button onclick="viewRegistrationDetail()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                <i class="fas fa-eye mr-1"></i>查看报名详情
                            </button>
                            <button onclick="showCancelConfirm()" class="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors text-sm">
                                <i class="fas fa-times mr-1"></i>取消报名
                            </button>
                        </div>
                    </div>
                `;
            } else if (canRegister(exhibition)) {
                statusRowHtml = `
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div class="flex items-center mb-3 sm:mb-0">
                            <i class="fas fa-user-plus text-blue-600 mr-2"></i>
                            <span class="text-lg font-semibold text-blue-800">欢迎报名参展</span>
                            <span class="ml-2 text-sm text-blue-600">请填写参展人员信息</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button onclick="showRegistrationModal()" class="px-6 py-2 btn-primary text-white rounded-lg text-sm">
                                <i class="fas fa-user-plus mr-1"></i>立即报名
                            </button>
                        </div>
                    </div>
                `;
            } else {
                const statusInfo = getExhibitionStatusInfo(exhibition);
                statusRowHtml = `
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-gray-600 mr-2"></i>
                        <span class="text-lg font-semibold text-gray-700">${statusInfo.main.text}</span>
                        <span class="ml-2 text-sm text-gray-500">当前无法报名</span>
                    </div>
                `;
            }

            $('#registrationStatusRow').html(statusRowHtml);
        }



        // 取消编辑
        function cancelEdit() {
            if (confirm('确定要取消编辑吗？未保存的修改将丢失。')) {
                // 清除编辑状态
                isEditMode = false;
                existingRegistrationData = null;

                // 移除URL中的edit参数并重新加载页面
                const url = new URL(window.location);
                url.searchParams.delete('edit');
                window.location.href = url.toString();
            }
        }

        // 智能返回到列表
        function goBackToList() {
            // 检查来源页面
            if (document.referrer) {
                try {
                    const referrerUrl = new URL(document.referrer);

                    // 如果来源页面是展览列表页面，直接返回
                    if (referrerUrl.pathname === '/pc_publisher_manage_exhibitions') {
                        window.location.href = '/pc_publisher_manage_exhibitions';
                        return;
                    }

                    // 如果来源页面是报名详情页面，返回列表
                    if (referrerUrl.pathname.includes('/registration')) {
                        window.location.href = '/pc_publisher_manage_exhibitions';
                        return;
                    }

                    // 如果来源页面是同域名的其他页面，且看起来像列表页面
                    if (referrerUrl.hostname === window.location.hostname &&
                        (referrerUrl.pathname.includes('manage') || referrerUrl.pathname.includes('list'))) {
                        window.location.href = document.referrer;
                        return;
                    }
                } catch (e) {
                    console.log('无法解析来源URL:', e);
                }
            }

            // 默认返回展览列表页面
            window.location.href = '/pc_publisher_manage_exhibitions';
        }

        // 获取展览状态信息
        function getExhibitionStatusInfo(exhibition) {
            const now = new Date();
            const startDate = exhibition.start_time ? new Date(exhibition.start_time) : null;
            const endDate = exhibition.end_time ? new Date(exhibition.end_time) : null;
            const deadlineDate = exhibition.registration_deadline ? new Date(exhibition.registration_deadline) : null;

            // 1. 判断是否已结束
            if (exhibition.status === 'ended' || (endDate && now > endDate)) {
                return {
                    main: { text: '已结束', class: 'bg-gray-100 text-gray-600', type: 'ended' },
                    secondary: null
                };
            }

            // 2. 判断是否在进行中
            if (startDate && endDate && now >= startDate && now <= endDate) {
                return {
                    main: { text: '进行中', class: 'bg-green-100 text-green-700', type: 'published' },
                    secondary: null
                };
            }

            // 3. 判断是否即将开始（3天内）
            if (startDate) {
                const threeDaysInMs = 3 * 24 * 60 * 60 * 1000;
                const timeDiff = startDate.getTime() - now.getTime();

                if (timeDiff > 0 && timeDiff <= threeDaysInMs) {
                    return {
                        main: { text: '即将开始', class: 'bg-yellow-100 text-yellow-700', type: 'upcoming' },
                        secondary: null
                    };
                }
            }

            // 4. 判断报名状态
            if (deadlineDate && now <= deadlineDate) {
                return {
                    main: { text: '可报名', class: 'bg-blue-100 text-blue-700', type: 'registerable' },
                    secondary: null
                };
            }

            // 5. 默认状态
            return {
                main: { text: '未开始', class: 'bg-gray-100 text-gray-600', type: 'draft' },
                secondary: null
            };
        }

        // 判断是否可以报名
        function canRegister(exhibition) {
            const now = new Date();
            const deadlineDate = exhibition.registration_deadline ? new Date(exhibition.registration_deadline) : null;
            const endDate = exhibition.end_time ? new Date(exhibition.end_time) : null;

            // 已结束的活动不能报名
            if (exhibition.status === 'ended' || (endDate && now > endDate)) {
                return false;
            }

            // 报名截止时间已过不能报名
            if (deadlineDate && now > deadlineDate) {
                return false;
            }

            return true;
        }

        // 显示报名模态框
        function showRegistrationModal() {
            // 设置模态框标题和按钮文字
            if (isEditMode) {
                $('#modalTitle').text('编辑报名信息');
                $('#submitButton').text('保存修改');

                // 编辑模式下检查是否有现有数据
                if (!existingRegistrationData) {
                    showMessage('无法获取现有报名数据，请稍后重试', 'error');
                    return;
                }
            } else {
                $('#modalTitle').text('报名参展');
                $('#submitButton').text('提交报名');
                // 初始化参展人员表单
                participantCount = 1;
            }

            updateRegistrationInstructions();
            renderParticipantsList();

            // 如果是编辑模式，填充现有数据
            if (isEditMode && existingRegistrationData) {
                setTimeout(() => {
                    fillExistingData();
                }, 100);
            }

            $('#registrationModal').removeClass('hidden');
        }

        // 更新报名说明
        function updateRegistrationInstructions() {
            const instructionsHtml = `
                <div class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <h4 class="text-sm font-medium text-amber-800 mb-2 flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>填写说明
                    </h4>
                    <ul class="text-sm text-amber-700 space-y-1">
                        <li>• 请为每位参展人员填写完整信息</li>
                        ${licensePlateRequired ?
                            '<li>• 本活动要求必须填写车牌号，如整个团队无车请勾选"整个团队无车参展"</li>' :
                            '<li>• 如需开车参展，请至少填写一个车牌号（可多人共用一车）</li>'
                        }
                        <li>• 多辆车可在任意参展人员处填写对应车牌号</li>
                        <li>• 必须选择一位联系人作为主要沟通对象</li>
                    </ul>
                </div>
            `;

            // 更新说明区域
            $('.registration-instructions').html(instructionsHtml);

            // 显示或隐藏无车参展选择
            if (licensePlateRequired) {
                $('#noCarSection').show();
            } else {
                $('#noCarSection').hide();
            }

            // 更新添加参展人员提示
            const tipText = licensePlateRequired ?
                '提示：如需添加更多参展人员，请确保填写车牌号或勾选整个团队无车参展' :
                '提示：如有多辆车需要停车，可在任意参展人员处填写车牌号';
            $('#addParticipantTip').text(tipText);
        }

        // 关闭报名模态框
        function closeRegistrationModal() {
            $('#registrationModal').addClass('hidden');
        }

        // 渲染参展人员列表
        function renderParticipantsList() {
            let html = '';
            for (let i = 1; i <= participantCount; i++) {
                html += `
                    <div class="participant-card rounded-lg p-4" data-index="${i}">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="font-medium text-gray-900">参展人员 ${i}</h4>
                            ${i > 1 ? `<button type="button" onclick="removeParticipant(${i})" class="text-red-600 hover:text-red-700 transition-colors"><i class="fas fa-trash"></i></button>` : ''}
                        </div>

                        <!-- 联系人选择 -->
                        <div class="mb-4 p-3 rounded-lg border transition-all contact-selection ${i === 1 ? 'selected' : ''}" id="contact_selection_${i}">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="contact_person" value="${i}" ${i === 1 ? 'checked' : ''}
                                       class="mr-3 text-blue-600 focus:ring-blue-500 w-4 h-4"
                                       onchange="updateContactSelection(${i})">
                                <span class="text-sm font-medium text-blue-700">
                                    <i class="fas fa-user-tie mr-1"></i>设为主要联系人
                                </span>
                            </label>
                            <p class="text-xs text-blue-600 mt-1 ml-7">
                                <i class="fas fa-info-circle mr-1"></i>
                                主要联系人将接收活动相关通知和沟通
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    姓名 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name_${i}" required
                                       placeholder="请输入真实姓名"
                                       class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    手机号 <span class="text-red-500">*</span>
                                </label>
                                <input type="tel" name="phone_${i}" required
                                       placeholder="请输入11位手机号码"
                                       pattern="[0-9]{11}"
                                       maxlength="11"
                                       class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    职务
                                </label>
                                <input type="text" name="role_${i}"
                                       placeholder="如：销售经理、业务代表等（选填）"
                                       maxlength="50"
                                       class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    车牌号 ${licensePlateRequired ? '<span class="text-red-500">*</span>' : ''}
                                </label>
                                <input type="text" name="license_plate_${i}"
                                       placeholder="如：粤A12345${licensePlateRequired ? '（必填）' : '（选填）'}"
                                       maxlength="8"
                                       class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all">
                                <p class="text-xs text-gray-500 mt-1">
                                    <i class="fas fa-car mr-1"></i>${licensePlateRequired ? '活动要求必须填写车牌号，如整个团队无车请在上方勾选' : '如需开车参展，请至少填写一个车牌号'}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            }
            $('#participantsList').html(html);

            // 绑定输入验证事件
            bindInputValidation();

            // 如果已经勾选了无车参展，需要禁用新添加的车牌输入框
            if ($('#noCarCheckbox').is(':checked')) {
                toggleAllCarInputs();
            }
        }

        // 绑定输入验证事件
        function bindInputValidation() {
            // 手机号输入验证
            $('input[name^="phone_"]').on('input', function() {
                const phone = $(this).val();
                const phoneRegex = /^1[3-9]\d{9}$/;

                if (phone && !phoneRegex.test(phone)) {
                    $(this).addClass('border-red-300 focus:border-red-500 focus:ring-red-500');
                    // 显示错误提示
                    if (!$(this).next('.error-tip').length) {
                        $(this).after('<p class="error-tip text-xs text-red-600 mt-1"><i class="fas fa-exclamation-circle mr-1"></i>请输入正确的手机号格式</p>');
                    }
                } else {
                    $(this).removeClass('border-red-300 focus:border-red-500 focus:ring-red-500');
                    $(this).next('.error-tip').remove();
                }
            });


            // 车牌号输入验证
            $('input[name^="license_plate_"]').on('input', function() {
                const licensePlate = $(this).val().toUpperCase();
                $(this).val(licensePlate); // 自动转换为大写

                // 支持新能源车牌（8位）和普通车牌（7位）
                const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/;

                if (licensePlate && !plateRegex.test(licensePlate)) {
                    $(this).addClass('border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500');
                    if (!$(this).next('.warning-tip').length) {
                        $(this).after('<p class="warning-tip text-xs text-yellow-600 mt-1"><i class="fas fa-exclamation-triangle mr-1"></i>请检查车牌号格式，如：粤A12345</p>');
                    }
                } else {
                    $(this).removeClass('border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500');
                    $(this).next('.warning-tip').remove();
                }
            });
        }

        // 更新联系人选择样式
        function updateContactSelection(selectedIndex) {
            // 移除所有选中样式
            $('.contact-selection').removeClass('selected');

            // 添加选中样式到当前选择
            $(`#contact_selection_${selectedIndex}`).addClass('selected');
        }

        // 切换所有车辆输入状态
        function toggleAllCarInputs() {
            const checkbox = $('#noCarCheckbox');
            const isChecked = checkbox.is(':checked');

            // 遍历所有车牌输入框
            for (let i = 1; i <= participantCount; i++) {
                const input = $(`input[name="license_plate_${i}"]`);

                if (isChecked) {
                    // 勾选无车，禁用所有车牌输入
                    input.prop('disabled', true);
                    input.val('');
                    input.addClass('bg-gray-100 cursor-not-allowed');
                    input.attr('placeholder', '整个团队无车参展');
                } else {
                    // 取消勾选，启用所有车牌输入
                    input.prop('disabled', false);
                    input.removeClass('bg-gray-100 cursor-not-allowed');
                    input.attr('placeholder', `如：粤A12345${licensePlateRequired ? '（必填）' : '（选填）'}`);
                }
            }
        }

        // 保存当前表单数据
        function saveCurrentFormData() {
            const formData = {};

            // 保存参展人员数据
            for (let i = 1; i <= participantCount; i++) {
                formData[`name_${i}`] = $(`input[name="name_${i}"]`).val() || '';
                formData[`phone_${i}`] = $(`input[name="phone_${i}"]`).val() || '';
                formData[`role_${i}`] = $(`input[name="role_${i}"]`).val() || '';
                formData[`license_plate_${i}`] = $(`input[name="license_plate_${i}"]`).val() || '';
            }

            // 保存联系人选择
            formData.contactPerson = $('input[name="contact_person"]:checked').val() || '1';

            // 保存无车状态
            formData.noCar = $('#noCarCheckbox').is(':checked');

            return formData;
        }

        // 恢复表单数据
        function restoreFormData(formData) {
            if (!formData) return;

            // 恢复参展人员数据
            for (let i = 1; i <= participantCount; i++) {
                if (formData[`name_${i}`]) {
                    $(`input[name="name_${i}"]`).val(formData[`name_${i}`]);
                }
                if (formData[`phone_${i}`]) {
                    $(`input[name="phone_${i}"]`).val(formData[`phone_${i}`]);
                }
                if (formData[`role_${i}`]) {
                    $(`input[name="role_${i}"]`).val(formData[`role_${i}`]);
                }
                if (formData[`license_plate_${i}`]) {
                    $(`input[name="license_plate_${i}"]`).val(formData[`license_plate_${i}`]);
                }
            }

            // 恢复联系人选择
            if (formData.contactPerson) {
                $(`input[name="contact_person"][value="${formData.contactPerson}"]`).prop('checked', true);
                updateContactSelection(parseInt(formData.contactPerson));
            }

            // 恢复无车状态
            if (formData.noCar) {
                $('#noCarCheckbox').prop('checked', true);
                toggleAllCarInputs();
            }
        }

        // 添加参展人员
        function addParticipant() {
            // 保存当前表单数据
            const currentFormData = saveCurrentFormData();

            // 增加参展人员数量
            participantCount++;

            // 重新渲染列表
            renderParticipantsList();

            // 恢复之前的表单数据
            setTimeout(() => {
                restoreFormData(currentFormData);
            }, 50);
        }

        // 移除参展人员
        function removeParticipant(index) {
            if (participantCount > 1) {
                // 保存当前表单数据
                const currentFormData = saveCurrentFormData();

                // 减少参展人员数量
                participantCount--;

                // 重新渲染列表
                renderParticipantsList();

                // 恢复表单数据（跳过被删除的人员）
                setTimeout(() => {
                    const adjustedFormData = {};
                    let newIndex = 1;

                    for (let i = 1; i <= participantCount + 1; i++) {
                        if (i !== index) {
                            adjustedFormData[`name_${newIndex}`] = currentFormData[`name_${i}`] || '';
                            adjustedFormData[`phone_${newIndex}`] = currentFormData[`phone_${i}`] || '';
                            adjustedFormData[`role_${newIndex}`] = currentFormData[`role_${i}`] || '';
                            adjustedFormData[`license_plate_${newIndex}`] = currentFormData[`license_plate_${i}`] || '';
                            newIndex++;
                        }
                    }

                    // 调整联系人选择
                    const oldContactPerson = parseInt(currentFormData.contactPerson || '1');
                    if (oldContactPerson === index) {
                        // 如果删除的是联系人，默认选择第一个
                        adjustedFormData.contactPerson = '1';
                    } else if (oldContactPerson > index) {
                        // 如果联系人在被删除人员之后，索引需要减1
                        adjustedFormData.contactPerson = (oldContactPerson - 1).toString();
                    } else {
                        // 联系人在被删除人员之前，索引不变
                        adjustedFormData.contactPerson = oldContactPerson.toString();
                    }

                    adjustedFormData.noCar = currentFormData.noCar;

                    restoreFormData(adjustedFormData);
                }, 50);
            }
        }

        // 提交报名
        function submitRegistration() {
            // 收集参展人员信息
            const participants = [];
            let contactPersonIndex = $('input[name="contact_person"]:checked').val();
            let hasLicensePlate = false; // 是否有人填写了车牌号
            const teamNoCar = $('#noCarCheckbox').is(':checked'); // 整个团队是否无车

            for (let i = 1; i <= participantCount; i++) {
                const name = $(`input[name="name_${i}"]`).val().trim();
                const phone = $(`input[name="phone_${i}"]`).val().trim();
                const role = $(`input[name="role_${i}"]`).val().trim();
                const licensePlate = $(`input[name="license_plate_${i}"]`).val().trim();

                // 验证必填项
                if (!name) {
                    showMessage(`参展人员${i}的姓名不能为空`, 'error');
                    $(`input[name="name_${i}"]`).focus();
                    return;
                }

                if (!phone) {
                    showMessage(`参展人员${i}的手机号不能为空`, 'error');
                    $(`input[name="phone_${i}"]`).focus();
                    return;
                }

                // 验证手机号格式
                const phoneRegex = /^1[3-9]\d{9}$/;
                if (!phoneRegex.test(phone)) {
                    showMessage(`参展人员${i}的手机号格式不正确`, 'error');
                    $(`input[name="phone_${i}"]`).focus();
                    return;
                }

                // 验证车牌号格式（如果填写了）
                if (licensePlate) {
                    const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4,5}[A-Z0-9挂学警港澳]$/;
                    if (!plateRegex.test(licensePlate)) {
                        showMessage(`参展人员${i}的车牌号格式不正确，请参考：粤A12345`, 'error');
                        $(`input[name="license_plate_${i}"]`).focus();
                        return;
                    }
                    hasLicensePlate = true;
                }

                participants.push({
                    name: name,
                    phone: phone,
                    role: role,
                    license_plate: teamNoCar ? '' : licensePlate, // 如果团队无车，车牌号为空
                    is_contact: i == contactPersonIndex
                });
            }

            // 车牌号验证逻辑
            if (licensePlateRequired) {
                // 如果活动要求必填车牌号
                if (!teamNoCar && !hasLicensePlate) {
                    showMessage('活动要求必须填写车牌号，请填写至少一个车牌号或勾选"整个团队无车参展"', 'error');
                    return;
                }
            } else {
                // 如果活动不要求必填车牌号，但没有人填写车牌号，给出提示（不阻止提交）
                if (!hasLicensePlate && !teamNoCar && !confirm('您没有填写任何车牌号，确定要继续提交吗？')) {
                    return;
                }
            }

            // 提交数据
            $.ajax({
                url: '/api/common/register_exhibition',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    exhibition_id: exhibitionId,
                    participants: participants
                }),
                success: function(response) {
                    if (response.code === 0) {
                        const message = isEditMode ? '修改报名信息成功！' : '报名成功！';
                        showMessage(message, 'success');
                        closeRegistrationModal();

                        if (isEditMode) {
                            // 编辑模式下，清除编辑状态并跳转到报名详情页面
                            isEditMode = false;
                            existingRegistrationData = null;

                            // 清除URL中的edit参数
                            const url = new URL(window.location);
                            url.searchParams.delete('edit');
                            window.history.replaceState({}, '', url.toString());

                            // 直接跳转到报名详情页面
                            setTimeout(() => {
                                window.location.href = `/publisher/exhibitions/${exhibitionId}/registration`;
                            }, 1500);
                        } else {
                            // 重新加载页面数据
                            loadExhibitionDetail();
                        }
                    } else {
                        showMessage(response.message || '报名失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 查看报名详情
        function viewRegistrationDetail() {
            // 这里可以跳转到报名详情页面或显示详情
            window.location.href = `/publisher/exhibitions/${exhibitionId}/registration`;
        }

        // 显示取消确认
        function showCancelConfirm() {
            if (confirm('确定要取消报名吗？取消后需要重新报名。')) {
                cancelRegistration();
            }
        }

        // 取消报名
        function cancelRegistration() {
            $.ajax({
                url: '/api/common/cancel_exhibition_registration',
                type: 'POST',
                data: { exhibition_id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        showMessage('取消报名成功', 'success');
                        // 重新加载页面数据
                        loadExhibitionDetail();
                    } else {
                        showMessage(response.message || '取消失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 显示消息
        function showMessage(text, type = 'info') {
            const messageId = Date.now();
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${messageId}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${messageId})"
                            class="flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(messageId);
            }, 5000);
        }

        // 移除消息
        function removeMessage(messageId) {
            const messageEl = document.getElementById(`message-${messageId}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }

        // 显示错误
        function showError(message) {
            $('#loadingState').addClass('hidden');
            $('#mainContent').removeClass('hidden');
            $('#mainContent').html(`
                <div class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">加载失败</h3>
                    <p class="text-gray-500 mb-4">${message}</p>
                    <button onclick="goBackToList()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        返回列表
                    </button>
                </div>
            `);
        }
    </script>
</body>
</html>
