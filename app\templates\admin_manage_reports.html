<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员报备管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <style>
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 状态标签 */
        .tag-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fbbf24;
        }

        .tag-approved {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border-color: #34d399;
        }

        .tag-rejected {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border-color: #f87171;
        }

        .tag-completed {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border-color: #81d4fa;
        }

        /* 推广状态标签 */
        .tag-promotion-pending {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        .tag-promotion-progress {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            border-color: #64b5f6;
        }

        .tag-promotion-success {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border-color: #a5d6a7;
        }

        .tag-promotion-failed {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
            border-color: #ef5350;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 1000;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 滚动条样式 */
        .custom-select-options::-webkit-scrollbar {
            width: 4px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        /* 卡片样式 */
        .report-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .report-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .report-card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .report-card-body {
            flex: 1;
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" x-data="reportManager()" x-init="initialize()">
        <!-- Tab栏和操作区 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 mb-8">
            <!-- Tab栏 -->
            <div class="border-b border-slate-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button @click="setStatusFilter('')"
                            :class="statusFilter === '' ? 'border-blue-500 text-blue-600' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        全部报备
                        <span x-show="statusCounts.total > 0"
                              :class="statusFilter === '' ? 'bg-blue-100 text-blue-600' : 'bg-slate-100 text-slate-600'"
                              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              x-text="statusCounts.total"></span>
                    </button>
                    <button @click="setStatusFilter('pending')"
                            :class="statusFilter === 'pending' ? 'border-yellow-500 text-yellow-600' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        待审核
                        <span x-show="statusCounts.pending > 0"
                              :class="statusFilter === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-slate-100 text-slate-600'"
                              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              x-text="statusCounts.pending"></span>
                    </button>
                    <button @click="setStatusFilter('approved')"
                            :class="statusFilter === 'approved' ? 'border-green-500 text-green-600' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        已通过
                        <span x-show="statusCounts.approved > 0"
                              :class="statusFilter === 'approved' ? 'bg-green-100 text-green-600' : 'bg-slate-100 text-slate-600'"
                              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              x-text="statusCounts.approved"></span>
                    </button>
                    <button @click="setStatusFilter('rejected')"
                            :class="statusFilter === 'rejected' ? 'border-red-500 text-red-600' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        已拒绝
                        <span x-show="statusCounts.rejected > 0"
                              :class="statusFilter === 'rejected' ? 'bg-red-100 text-red-600' : 'bg-slate-100 text-slate-600'"
                              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              x-text="statusCounts.rejected"></span>
                    </button>
                    <button @click="setStatusFilter('completed')"
                            :class="statusFilter === 'completed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                        已完成
                        <span x-show="statusCounts.completed > 0"
                              :class="statusFilter === 'completed' ? 'bg-blue-100 text-blue-600' : 'bg-slate-100 text-slate-600'"
                              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              x-text="statusCounts.completed"></span>
                    </button>
                </nav>
            </div>

            <!-- 搜索和筛选区 -->
            <div class="p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <!-- 搜索和筛选 -->
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
                        <!-- 搜索框 -->
                        <div class="flex-1 max-w-md">
                            <input type="text"
                                   x-model="searchKeyword"
                                   @input.debounce.500ms="loadReports()"
                                   placeholder="搜索样书名称、作者、学校..."
                                   class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <!-- 出版社筛选 -->
                        <div class="custom-select" id="publisherContainer">
                            <div class="custom-select-trigger" id="publisherTrigger">
                                <span class="custom-select-text">全部出版社</span>
                                <i class="fas fa-chevron-down custom-select-arrow"></i>
                            </div>
                            <div class="custom-select-dropdown">
                                <div class="custom-select-search">
                                    <input type="text" placeholder="搜索出版社...">
                                </div>
                                <div class="custom-select-options" id="publisherOptions">
                                    <!-- 选项将动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-3">
                        <button @click="openAddModal()"
                                class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                            <i class="fas fa-plus"></i>
                            <span>添加报备</span>
                        </button>
                        <button @click="loadReports()"
                                class="h-12 px-6 bg-slate-100 text-slate-700 rounded-xl flex items-center space-x-2 hover:bg-slate-200 transition-colors">
                            <i class="fas fa-sync-alt"></i>
                            <span>刷新</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报备列表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" x-show="!loading">
            <template x-for="report in reports" :key="report.id">
                <article class="report-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden hover:border-slate-200">
                    <div class="report-card-content">
                        <!-- 卡片头部 -->
                        <div class="p-6 pb-4">
                            <div class="flex items-start justify-between mb-3">
                                <h3 class="text-lg font-semibold text-slate-800 line-clamp-2 leading-tight" x-text="report.sample_name"></h3>
                                <div class="flex space-x-2 ml-4">
                                    <span :class="getStatusTagClass(report.status)" x-text="getStatusText(report.status)"></span>
                                </div>
                            </div>
                            <div class="space-y-2 text-sm text-slate-600">
                                <div class="flex items-center">
                                    <i class="fas fa-user text-slate-400 mr-2 w-4"></i>
                                    <span x-text="report.author || '未填写'"></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-school text-slate-400 mr-2 w-4"></i>
                                    <span x-text="report.school_name"></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-store text-slate-400 mr-2 w-4"></i>
                                    <span x-text="getDealerDisplayName(report)"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片主体 -->
                        <div class="report-card-body px-6 pb-4">
                            <div class="space-y-3">
                                <!-- 推广状态 -->
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-slate-500">推广状态</span>
                                    <span :class="getPromotionStatusTagClass(report.promotion_status)" x-text="getPromotionStatusText(report.promotion_status)"></span>
                                </div>
                                
                                <!-- 创建时间 -->
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-slate-500">创建时间</span>
                                    <span class="text-sm text-slate-700" x-text="report.created_at"></span>
                                </div>
                                
                                <!-- 有效期 -->
                                <div class="flex items-center justify-between" x-show="report.expiry_date">
                                    <span class="text-sm text-slate-500">有效期</span>
                                    <span class="text-sm text-slate-700" x-text="report.expiry_date"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片底部操作 -->
                        <div class="px-6 py-4 bg-slate-50 border-t border-slate-100">
                            <div class="flex justify-end space-x-2">
                                <button @click="viewDetail(report)" 
                                        class="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                                <button @click="editReport(report)" 
                                        class="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button @click="deleteReport(report.id)" 
                                        class="px-4 py-2 text-sm bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                                    <i class="fas fa-trash mr-1"></i>删除
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
            </template>
        </div>

        <!-- 加载状态 -->
        <div x-show="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <!-- 空状态 -->
        <div x-show="!loading && reports.length === 0" class="text-center py-12">
            <div class="text-slate-400 text-6xl mb-4">
                <i class="fas fa-file-alt"></i>
            </div>
            <h3 class="text-lg font-medium text-slate-600 mb-2">暂无报备数据</h3>
            <p class="text-slate-500">点击"添加报备"按钮创建第一个报备</p>
        </div>

        <!-- 分页 -->
        <div x-show="!loading && reports.length > 0" class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
            <!-- 信息显示区域 -->
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span x-text="currentPage" class="font-medium"></span> 页，
                    共 <span x-text="totalPages" class="font-medium"></span> 页，
                    共 <span x-text="totalCount" class="font-medium"></span> 条
                </p>
            </div>

            <!-- 分页按钮区域 -->
            <div class="flex gap-1">
                <!-- 首页按钮 -->
                <button @click="goToPage(1)"
                        :disabled="currentPage <= 1"
                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">首页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- 上一页按钮 -->
                <button @click="goToPage(currentPage - 1)"
                        :disabled="currentPage <= 1"
                        class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>

                <!-- 页码按钮 -->
                <template x-for="page in getPageNumbers()" :key="page">
                    <button x-show="page !== '...'"
                            @click="goToPage(page)"
                            :class="page === currentPage ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700'"
                            class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50"
                            x-text="page">
                    </button>
                    <span x-show="page === '...'"
                          class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                        ...
                    </span>
                </template>

                <!-- 下一页按钮 -->
                <button @click="goToPage(currentPage + 1)"
                        :disabled="currentPage >= totalPages"
                        class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>

                <!-- 末页按钮 -->
                <button @click="goToPage(totalPages)"
                        :disabled="currentPage >= totalPages"
                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">末页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div id="modalBody" class="overflow-y-auto max-h-[70vh] custom-scrollbar"></div>
                <div id="modalFooter" class="p-6 border-t border-slate-200 bg-slate-50"></div>
            </div>
        </div>
    </div>

    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        let messageId = 0;

        // 消息通知函数
        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }

        // 模态框函数
        function openModal(title, content, footer = '') {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modalFooter').innerHTML = footer;
            document.getElementById('modalContainer').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('modalContainer').classList.add('hidden');
        }

        // 自定义搜索下拉框类
        class CustomSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                this.trigger = this.container.querySelector('.custom-select-trigger');
                this.dropdown = this.container.querySelector('.custom-select-dropdown');
                this.searchInput = this.container.querySelector('.custom-select-search input');
                this.optionsContainer = this.container.querySelector('.custom-select-options');
                this.textSpan = this.trigger.querySelector('.custom-select-text');

                this.options = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择';
                this.onSelect = options.onSelect || null;

                this.init();
            }

            init() {
                // 绑定事件
                this.trigger.addEventListener('click', () => {
                    this.toggle();
                });

                // 搜索功能
                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });
                }

                // 点击选项
                this.optionsContainer.addEventListener('click', (e) => {
                    const option = e.target.closest('.custom-select-option');
                    if (option && !option.classList.contains('no-results')) {
                        const value = option.dataset.value;
                        const text = option.textContent;
                        this.selectOption(value, text);
                    }
                });

                // 点击外部关闭
                document.addEventListener('click', (e) => {
                    if (!this.container.contains(e.target)) {
                        this.close();
                    }
                });
            }

            setOptions(options) {
                this.options = options;
                this.renderOptions();
            }

            setValue(value) {
                const option = this.options.find(opt => opt.value === value);
                if (option) {
                    this.selectOption(value, option.text);
                }
            }

            getValue() {
                return this.selectedValue;
            }

            reset() {
                this.selectedValue = '';
                this.selectedText = '';
                this.textSpan.textContent = this.placeholder;
                if (this.searchInput) {
                    this.searchInput.value = '';
                }
                this.renderOptions();
                this.close();
            }

            toggle() {
                if (this.container.classList.contains('active')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                this.container.classList.add('active');
                if (this.searchInput) {
                    setTimeout(() => this.searchInput.focus(), 100);
                }
            }

            close() {
                this.container.classList.remove('active');
            }

            selectOption(value, text) {
                this.selectedValue = value;
                this.selectedText = text;
                this.textSpan.textContent = text;
                this.close();

                if (this.onSelect) {
                    this.onSelect(value, text);
                }
            }

            renderOptions() {
                this.optionsContainer.innerHTML = '';

                if (this.options.length === 0) {
                    this.optionsContainer.innerHTML = '<div class="custom-select-option no-results">暂无选项</div>';
                    return;
                }

                this.options.forEach(option => {
                    const optionEl = document.createElement('div');
                    optionEl.className = 'custom-select-option';
                    optionEl.dataset.value = option.value;
                    optionEl.textContent = option.text;

                    if (option.value === this.selectedValue) {
                        optionEl.classList.add('selected');
                    }

                    this.optionsContainer.appendChild(optionEl);
                });
            }

            filterOptions(searchTerm) {
                const filteredOptions = this.options.filter(option =>
                    option.text.toLowerCase().includes(searchTerm.toLowerCase())
                );

                this.optionsContainer.innerHTML = '';

                if (filteredOptions.length === 0) {
                    this.optionsContainer.innerHTML = '<div class="custom-select-option no-results">无匹配结果</div>';
                    return;
                }

                filteredOptions.forEach(option => {
                    const optionEl = document.createElement('div');
                    optionEl.className = 'custom-select-option';
                    optionEl.dataset.value = option.value;
                    optionEl.textContent = option.text;

                    if (option.value === this.selectedValue) {
                        optionEl.classList.add('selected');
                    }

                    this.optionsContainer.appendChild(optionEl);
                });
            }
        }

        // 报备管理主函数
        function reportManager() {
            return {
                // 数据状态
                reports: [],
                loading: false,
                searchKeyword: '',
                statusFilter: '',
                publisherFilter: '',
                selectedSamples: [],

                // 状态统计
                statusCounts: {
                    total: 0,
                    pending: 0,
                    approved: 0,
                    rejected: 0,
                    completed: 0
                },

                // 分页状态
                currentPage: 1,
                totalPages: 1,
                totalCount: 0,
                pageSize: 12,

                // 初始化
                initialize() {
                    this.initPublisherSelect();
                    this.loadReports();
                    this.loadStatusCounts();

                    // 监听样书选择器的消息
                    window.addEventListener('message', (event) => {
                        console.log('收到消息:', event.data);
                        if (event.data.type === 'SELECTED_BOOKS_FROM_SELECTOR') {
                            console.log('处理选中的样书:', event.data.books);
                            this.handleSelectedBooks(event.data.books);
                        }
                    });
                },

                // 设置状态筛选
                setStatusFilter(status) {
                    this.statusFilter = status;
                    this.currentPage = 1;
                    this.loadReports();
                },

                // 初始化出版社选择器
                initPublisherSelect() {
                    this.publisherSelect = new CustomSelect('publisherContainer', {
                        placeholder: '全部出版社',
                        onSelect: (value, text) => {
                            this.publisherFilter = value;
                            this.currentPage = 1;
                            this.loadReports();
                        }
                    });

                    // 加载出版社列表
                    this.loadPublishers();
                },

                // 加载出版社列表
                async loadPublishers() {
                    try {
                        const response = await fetch('/api/admin/get_publishers_for_report');
                        const data = await response.json();

                        if (data.code === 0) {
                            const options = [{ value: '', text: '全部出版社' }];
                            data.data.forEach(publisher => {
                                options.push({
                                    value: publisher.id,
                                    text: publisher.name
                                });
                            });
                            this.publisherSelect.setOptions(options);
                        }
                    } catch (error) {
                        console.error('加载出版社列表失败:', error);
                    }
                },

                // 加载状态统计
                async loadStatusCounts() {
                    try {
                        const response = await fetch('/api/admin/get_report_status_counts');
                        const data = await response.json();

                        if (data.code === 0) {
                            this.statusCounts = data.data;
                        }
                    } catch (error) {
                        console.error('加载状态统计失败:', error);
                    }
                },

                // 加载报备列表
                async loadReports() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            page: this.currentPage,
                            page_size: this.pageSize,
                            search: this.searchKeyword,
                            status: this.statusFilter,
                            publisher_id: this.publisherFilter
                        });

                        const response = await fetch(`/api/admin/get_all_reports?${params}`);
                        const data = await response.json();

                        if (data.code === 0) {
                            this.reports = data.data;
                            this.totalCount = data.total;
                            this.totalPages = Math.ceil(data.total / this.pageSize);
                        } else {
                            showMessage(data.message || '加载报备列表失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // 分页相关方法
                getPageNumbers() {
                    const pageNumbers = [];

                    if (this.totalPages <= 7) {
                        for (let i = 1; i <= this.totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        pageNumbers.push(1);

                        if (this.currentPage <= 4) {
                            pageNumbers.push(2, 3, 4, 5);
                            pageNumbers.push('...');
                            pageNumbers.push(this.totalPages);
                        } else if (this.currentPage >= this.totalPages - 3) {
                            pageNumbers.push('...');
                            pageNumbers.push(this.totalPages - 4, this.totalPages - 3, this.totalPages - 2, this.totalPages - 1);
                            pageNumbers.push(this.totalPages);
                        } else {
                            pageNumbers.push('...');
                            pageNumbers.push(this.currentPage - 1, this.currentPage, this.currentPage + 1);
                            pageNumbers.push('...');
                            pageNumbers.push(this.totalPages);
                        }
                    }

                    return pageNumbers;
                },

                goToPage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                        this.loadReports();
                    }
                },

                // 经销商显示格式
                getDealerDisplayName(report) {
                    if (report.dealer_company_name && report.dealer_name) {
                        return `${report.dealer_company_name}(${report.dealer_name})`;
                    } else if (report.dealer_company_name) {
                        return report.dealer_company_name;
                    } else {
                        return report.dealer_name || '未知经销商';
                    }
                },

                // 处理选中的样书
                handleSelectedBooks(books) {
                    console.log('handleSelectedBooks 被调用，新增样书数量:', books.length);

                    // 累加样书，去除重复
                    books.forEach(newBook => {
                        // 检查是否已存在（根据ID判断）
                        const exists = this.selectedSamples.some(existingBook => existingBook.id === newBook.id);
                        if (!exists) {
                            // 为每本样书添加选中状态
                            newBook.selected = false;
                            this.selectedSamples.push(newBook);
                        }
                    });

                    this.updateSelectedSamplesDisplay();
                    console.log('样书选择完成，当前总数:', this.selectedSamples.length);
                },

                // 更新选中样书的显示
                updateSelectedSamplesDisplay() {
                    console.log('updateSelectedSamplesDisplay 被调用');
                    const container = document.getElementById('selectedSamples');
                    if (!container) {
                        console.log('未找到 selectedSamples 容器');
                        return;
                    }

                    console.log('当前选中样书数量:', this.selectedSamples.length);
                    if (this.selectedSamples.length === 0) {
                        container.innerHTML = '<div class="text-sm text-slate-500 text-center py-8">尚未选择样书</div>';
                        return;
                    }

                    const html = `
                        <div class="bg-white rounded-lg border overflow-hidden">
                            <!-- 表格头部 -->
                            <div class="bg-slate-50 px-4 py-3 border-b flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="selectAllSamples"
                                               onchange="toggleAllSamples(this.checked)"
                                               class="rounded border-slate-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm font-medium text-slate-700">全选</span>
                                    </label>
                                    <span class="text-sm text-slate-600">已选择 ${this.selectedSamples.length} 本样书</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button type="button" onclick="removeSelectedSamples()"
                                            class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                                        <i class="fas fa-trash mr-1"></i>删除选中
                                    </button>
                                    <button type="button" onclick="clearAllSamples()"
                                            class="px-3 py-1 text-sm bg-slate-500 text-white rounded hover:bg-slate-600 transition-colors">
                                        <i class="fas fa-times mr-1"></i>清空全部
                                    </button>
                                </div>
                            </div>

                            <!-- 表格内容 -->
                            <div class="max-h-64 overflow-y-auto">
                                <table class="w-full">
                                    <thead class="bg-slate-50 sticky top-0">
                                        <tr class="text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                            <th class="px-4 py-2 w-12">选择</th>
                                            <th class="px-4 py-2">样书名称</th>
                                            <th class="px-4 py-2">作者</th>
                                            <th class="px-4 py-2">出版社</th>
                                            <th class="px-4 py-2">价格</th>
                                            <th class="px-4 py-2 w-20">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-slate-200">
                                        ${this.selectedSamples.map((book, index) => `
                                            <tr class="hover:bg-slate-50">
                                                <td class="px-4 py-3">
                                                    <input type="checkbox"
                                                           class="sample-checkbox rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                                                           data-book-id="${book.id}"
                                                           onchange="updateSelectAllState()">
                                                </td>
                                                <td class="px-4 py-3">
                                                    <div class="font-medium text-sm text-slate-900">${book.name}</div>
                                                    <div class="text-xs text-slate-500">ISBN: ${book.isbn || '未填写'}</div>
                                                </td>
                                                <td class="px-4 py-3 text-sm text-slate-700">${book.author || '未知作者'}</td>
                                                <td class="px-4 py-3 text-sm text-slate-700">${book.publisher_name || '未知出版社'}</td>
                                                <td class="px-4 py-3 text-sm text-slate-700">¥${book.price || '0.00'}</td>
                                                <td class="px-4 py-3">
                                                    <button type="button" onclick="removeSelectedSample(${book.id})"
                                                            class="text-red-500 hover:text-red-700 text-sm">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;

                    container.innerHTML = html;
                },

                // 移除选中的样书
                removeSelectedSample(bookId) {
                    this.selectedSamples = this.selectedSamples.filter(book => book.id !== bookId);
                    this.updateSelectedSamplesDisplay();
                },

                // 状态标签样式
                getStatusTagClass(status) {
                    const baseClass = 'tag text-xs px-2 py-1 rounded-full';
                    switch (status) {
                        case 'pending': return baseClass + ' tag-pending';
                        case 'approved': return baseClass + ' tag-approved';
                        case 'rejected': return baseClass + ' tag-rejected';
                        case 'completed': return baseClass + ' tag-completed';
                        default: return baseClass + ' tag-pending';
                    }
                },

                getStatusText(status) {
                    switch (status) {
                        case 'pending': return '待审核';
                        case 'approved': return '已通过';
                        case 'rejected': return '已拒绝';
                        case 'completed': return '已完成';
                        default: return '未知';
                    }
                },

                getPromotionStatusTagClass(status) {
                    const baseClass = 'tag text-xs px-2 py-1 rounded-full';
                    switch (status) {
                        case 'pending': return baseClass + ' tag-promotion-pending';
                        case 'in_progress': return baseClass + ' tag-promotion-progress';
                        case 'successful': return baseClass + ' tag-promotion-success';
                        case 'failed': return baseClass + ' tag-promotion-failed';
                        default: return baseClass + ' tag-promotion-pending';
                    }
                },

                getPromotionStatusText(status) {
                    switch (status) {
                        case 'pending': return '待推广';
                        case 'in_progress': return '推广中';
                        case 'successful': return '推广成功';
                        case 'failed': return '推广失败';
                        default: return '待推广';
                    }
                },

                // 查看详情
                async viewDetail(report) {
                    try {
                        const response = await fetch(`/api/admin/get_report_detail/${report.id}`);
                        const data = await response.json();

                        if (data.code === 0) {
                            this.showDetailModal(data.data);
                        } else {
                            showMessage(data.message || '获取报备详情失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 显示详情模态框
                showDetailModal(report) {
                    const content = `
                        <div class="p-6 space-y-6">
                            <!-- 样书信息 -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4">样书信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">样书名称</label>
                                        <p class="text-slate-800">${report.sample_name || '未填写'}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">作者</label>
                                        <p class="text-slate-800">${report.author || '未填写'}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">ISBN</label>
                                        <p class="text-slate-800">${report.isbn || '未填写'}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">价格</label>
                                        <p class="text-slate-800">¥${report.price || '0.00'}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">出版社</label>
                                        <p class="text-slate-800">${report.publisher_company_name || report.publisher_name || '未设置'}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">层次</label>
                                        <p class="text-slate-800">${report.level || '未设置'}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 报备信息 -->
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4">报备信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">学校名称</label>
                                        <p class="text-slate-800">${report.school_name}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">经销商</label>
                                        <p class="text-slate-800">${this.getDealerDisplayName(report)}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">报备状态</label>
                                        <span class="${this.getStatusTagClass(report.status)}">${this.getStatusText(report.status)}</span>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">推广状态</label>
                                        <span class="${this.getPromotionStatusTagClass(report.promotion_status)}">${this.getPromotionStatusText(report.promotion_status)}</span>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">创建时间</label>
                                        <p class="text-slate-800">${report.created_at}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-slate-600">更新时间</label>
                                        <p class="text-slate-800">${report.updated_at || '未更新'}</p>
                                    </div>
                                    ${report.expiry_date ? `
                                        <div>
                                            <label class="text-sm font-medium text-slate-600">有效期</label>
                                            <p class="text-slate-800">${report.expiry_date}</p>
                                        </div>
                                    ` : ''}
                                    ${report.reason ? `
                                        <div class="md:col-span-2">
                                            <label class="text-sm font-medium text-slate-600">报备原因</label>
                                            <p class="text-slate-800">${report.reason}</p>
                                        </div>
                                    ` : ''}
                                    ${report.conflict_reason ? `
                                        <div class="md:col-span-2">
                                            <label class="text-sm font-medium text-slate-600">冲突原因</label>
                                            <p class="text-slate-800 text-red-600">${report.conflict_reason}</p>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;

                    const footer = `
                        <div class="flex justify-end space-x-3">
                            <button onclick="closeModal()"
                                    class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                关闭
                            </button>
                        </div>
                    `;

                    openModal('报备详情', content, footer);
                },

                // 编辑报备
                editReport(report) {
                    this.showEditModal(report);
                },

                // 显示编辑模态框
                showEditModal(report) {
                    const content = `
                        <form id="editReportForm" class="p-6 space-y-6">
                            <input type="hidden" name="report_id" value="${report.id}">

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">报备状态</label>
                                    <select name="status" onchange="toggleEditRejectReason(this.value)" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="pending" ${report.status === 'pending' ? 'selected' : ''}>待审核</option>
                                        <option value="approved" ${report.status === 'approved' ? 'selected' : ''}>已通过</option>
                                        <option value="rejected" ${report.status === 'rejected' ? 'selected' : ''}>已拒绝</option>
                                        <option value="completed" ${report.status === 'completed' ? 'selected' : ''}>已完成</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">推广状态</label>
                                    <select name="promotion_status" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="pending" ${report.promotion_status === 'pending' ? 'selected' : ''}>待推广</option>
                                        <option value="in_progress" ${report.promotion_status === 'in_progress' ? 'selected' : ''}>推广中</option>
                                        <option value="successful" ${report.promotion_status === 'successful' ? 'selected' : ''}>推广成功</option>
                                        <option value="failed" ${report.promotion_status === 'failed' ? 'selected' : ''}>推广失败</option>
                                    </select>
                                </div>
                            </div>

                            <div id="editRejectReasonContainer" style="display: ${report.status === 'rejected' ? 'block' : 'none'};">
                                <label class="block text-sm font-medium text-slate-700 mb-2">拒绝原因 *</label>
                                <textarea name="reason" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请填写拒绝原因...">${report.reason || ''}</textarea>
                            </div>
                        </form>
                    `;

                    const footer = `
                        <div class="flex justify-end space-x-3">
                            <button onclick="closeModal()"
                                    class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                取消
                            </button>
                            <button onclick="submitEditReport()"
                                    class="btn-success px-6 py-3 text-white rounded-xl">
                                保存
                            </button>
                        </div>
                    `;

                    openModal('编辑报备', content, footer);
                },

                // 删除报备
                async deleteReport(reportId) {
                    if (!confirm('确定要删除这个报备吗？此操作不可恢复。')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/api/admin/delete_report/${reportId}`, {
                            method: 'DELETE'
                        });
                        const data = await response.json();

                        if (data.code === 0) {
                            showMessage('报备删除成功', 'success');
                            this.loadReports();
                        } else {
                            showMessage(data.message || '删除报备失败', 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 打开添加模态框
                openAddModal() {
                    this.showAddModal();
                },

                // 显示添加模态框
                showAddModal() {
                    const content = `
                        <form id="addReportForm" class="p-6 space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">经销商 *</label>
                                    <div class="custom-select" id="dealerSelect">
                                        <div class="custom-select-trigger">
                                            <span class="custom-select-text">请选择经销商</span>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                        </div>
                                        <div class="custom-select-dropdown">
                                            <div class="custom-select-search">
                                                <input type="text" placeholder="搜索经销商...">
                                            </div>
                                            <div class="custom-select-options"></div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">学校 *</label>
                                    <div class="custom-select" id="schoolSelect">
                                        <div class="custom-select-trigger">
                                            <span class="custom-select-text">请选择学校</span>
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                        </div>
                                        <div class="custom-select-dropdown">
                                            <div class="custom-select-search">
                                                <input type="text" placeholder="搜索学校...">
                                            </div>
                                            <div class="custom-select-options"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-slate-700 mb-2">样书 *</label>
                                <div class="border border-slate-300 rounded-xl bg-slate-50">
                                    <div class="p-4 border-b border-slate-200">
                                        <button type="button" onclick="openBookSelector()"
                                                class="w-full px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                            <i class="fas fa-book-open mr-2"></i>选择样书
                                        </button>
                                    </div>
                                    <div id="selectedSamples" class="p-4">
                                        <div class="text-sm text-slate-500 text-center py-8">尚未选择样书</div>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">报备状态</label>
                                    <select name="status" onchange="toggleRejectReason(this.value)" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="pending">待审核</option>
                                        <option value="approved">已通过</option>
                                        <option value="rejected">已拒绝</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">推广状态</label>
                                    <select name="promotion_status" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="pending">待推广</option>
                                        <option value="in_progress">推广中</option>
                                        <option value="successful">推广成功</option>
                                        <option value="failed">推广失败</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">有效期</label>
                                <input type="date" name="expiry_date"
                                       class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div id="rejectReasonContainer" style="display: none;">
                                <label class="block text-sm font-medium text-slate-700 mb-2">拒绝原因 *</label>
                                <textarea name="reason" rows="3"
                                          class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请输入拒绝原因..."></textarea>
                            </div>
                        </form>
                    `;

                    const footer = `
                        <div class="flex justify-end space-x-3">
                            <button onclick="closeModal()"
                                    class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                取消
                            </button>
                            <button onclick="submitAddReport()"
                                    class="btn-primary px-6 py-3 text-white rounded-xl">
                                添加
                            </button>
                        </div>
                    `;

                    openModal('添加报备', content, footer);

                    // 初始化选择器
                    setTimeout(() => {
                        this.initAddModalSelects();
                    }, 100);
                },

                // 初始化添加模态框的选择器
                async initAddModalSelects() {
                    // 初始化经销商选择器
                    this.dealerSelect = new CustomSelect('dealerSelect', {
                        placeholder: '请选择经销商'
                    });

                    // 初始化学校选择器
                    this.schoolSelect = new CustomSelect('schoolSelect', {
                        placeholder: '请选择学校'
                    });

                    // 加载经销商数据
                    await this.loadDealers();

                    // 加载学校数据
                    await this.loadSchools();

                    // 重置选中的样书
                    this.selectedSamples = [];
                    this.updateSelectedSamplesDisplay();
                },

                // 加载经销商列表
                async loadDealers() {
                    try {
                        const response = await fetch('/api/admin/get_dealers_for_report');
                        const data = await response.json();

                        if (data.code === 0) {
                            const options = data.data.map(dealer => ({
                                value: dealer.id,
                                text: `${dealer.company_name || dealer.name} (${dealer.name})`
                            }));
                            this.dealerSelect.setOptions(options);
                        }
                    } catch (error) {
                        console.error('加载经销商列表失败:', error);
                    }
                },

                // 加载学校列表
                async loadSchools() {
                    try {
                        const response = await fetch('/api/admin/get_schools');
                        const data = await response.json();

                        if (data.code === 0) {
                            const options = data.data.map(school => ({
                                value: school.name,
                                text: school.name
                            }));
                            this.schoolSelect.setOptions(options);
                        }
                    } catch (error) {
                        console.error('加载学校列表失败:', error);
                    }
                }
            }
        }

        // 提交编辑报备
        async function submitEditReport() {
            const form = document.getElementById('editReportForm');
            const formData = new FormData(form);

            // 验证拒绝原因
            const status = formData.get('status');
            const reason = formData.get('reason');

            if (status === 'rejected' && !reason.trim()) {
                showMessage('拒绝状态必须填写拒绝原因', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/admin/update_report_status', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();

                if (data.code === 0) {
                    showMessage(data.message || '报备更新成功', 'success');
                    closeModal();
                    // 重新加载列表
                    const component = Alpine.$data(document.querySelector('[x-data="reportManager()"]'));
                    if (component) {
                        component.loadReports();
                        component.loadStatusCounts();
                    }
                } else {
                    showMessage(data.message || '更新报备失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 打开公共样书选择器
        function openBookSelector() {
            // 获取屏幕尺寸，使窗口最大化
            const width = screen.availWidth;
            const height = screen.availHeight;
            const left = 0;
            const top = 0;
            window.open('/common/book_selector', 'bookSelectorWindow', `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes`);
        }

        // 移除选中的样书
        function removeSelectedSample(bookId) {
            // 直接通过Alpine.js的全局访问方式
            const component = Alpine.$data(document.querySelector('[x-data="reportManager()"]'));
            if (component) {
                component.removeSelectedSample(bookId);
            }
        }

        // 全选/取消全选
        function toggleAllSamples(checked) {
            const checkboxes = document.querySelectorAll('.sample-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
            });
        }

        // 更新全选状态
        function updateSelectAllState() {
            const checkboxes = document.querySelectorAll('.sample-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllSamples');

            if (selectAllCheckbox) {
                const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
                selectAllCheckbox.checked = checkedCount === checkboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            }
        }

        // 删除选中的样书
        function removeSelectedSamples() {
            const component = Alpine.$data(document.querySelector('[x-data="reportManager()"]'));
            if (!component) return;

            const checkboxes = document.querySelectorAll('.sample-checkbox:checked');
            const bookIdsToRemove = Array.from(checkboxes).map(cb => parseInt(cb.dataset.bookId));

            if (bookIdsToRemove.length === 0) {
                showMessage('请先选择要删除的样书', 'warning');
                return;
            }

            if (confirm(`确定要删除选中的 ${bookIdsToRemove.length} 本样书吗？`)) {
                bookIdsToRemove.forEach(bookId => {
                    component.removeSelectedSample(bookId);
                });
                showMessage(`已删除 ${bookIdsToRemove.length} 本样书`, 'success');
            }
        }

        // 清空所有样书
        function clearAllSamples() {
            const component = Alpine.$data(document.querySelector('[x-data="reportManager()"]'));
            if (!component) return;

            if (component.selectedSamples.length === 0) {
                showMessage('没有可清空的样书', 'info');
                return;
            }

            if (confirm(`确定要清空所有 ${component.selectedSamples.length} 本样书吗？`)) {
                component.selectedSamples = [];
                component.updateSelectedSamplesDisplay();
                showMessage('已清空所有样书', 'success');
            }
        }

        // 控制拒绝原因输入框的显示（添加报备）
        function toggleRejectReason(status) {
            const container = document.getElementById('rejectReasonContainer');
            const textarea = container.querySelector('textarea[name="reason"]');

            if (status === 'rejected') {
                container.style.display = 'block';
                textarea.required = true;
            } else {
                container.style.display = 'none';
                textarea.required = false;
                textarea.value = ''; // 清空内容
            }
        }

        // 控制拒绝原因输入框的显示（编辑报备）
        function toggleEditRejectReason(status) {
            const container = document.getElementById('editRejectReasonContainer');
            const textarea = container.querySelector('textarea[name="reason"]');

            if (status === 'rejected') {
                container.style.display = 'block';
                textarea.required = true;
            } else {
                container.style.display = 'none';
                textarea.required = false;
                if (status !== 'rejected') {
                    textarea.value = ''; // 清空内容（除非是从拒绝状态切换过来）
                }
            }
        }

        // 提交添加报备
        async function submitAddReport() {
            const form = document.getElementById('addReportForm');
            const formData = new FormData(form);

            // 获取选择器的值
            const component = Alpine.$data(document.querySelector('[x-data="reportManager()"]'));
            const dealerSelect = component.dealerSelect;
            const schoolSelect = component.schoolSelect;
            const selectedSamples = component.selectedSamples;

            if (!dealerSelect.getValue()) {
                showMessage('请选择经销商', 'warning');
                return;
            }

            if (!schoolSelect.getValue()) {
                showMessage('请选择学校', 'warning');
                return;
            }

            if (selectedSamples.length === 0) {
                showMessage('请选择至少一本样书', 'warning');
                return;
            }

            // 验证拒绝原因
            const status = formData.get('status');
            const reason = formData.get('reason');

            if (status === 'rejected' && !reason.trim()) {
                showMessage('拒绝状态必须填写拒绝原因', 'warning');
                return;
            }

            // 批量提交报备
            let successCount = 0;
            let failedBooks = [];

            for (const sample of selectedSamples) {
                const reportData = new FormData();
                reportData.append('dealer_id', dealerSelect.getValue());
                reportData.append('sample_book_id', sample.id);
                reportData.append('school_name', schoolSelect.getValue());
                reportData.append('status', status);
                reportData.append('promotion_status', formData.get('promotion_status'));
                reportData.append('expiry_date', formData.get('expiry_date'));
                reportData.append('reason', reason);

                try {
                    const response = await fetch('/api/admin/add_report', {
                        method: 'POST',
                        body: reportData
                    });
                    const data = await response.json();

                    if (data.code === 0) {
                        successCount++;
                    } else {
                        failedBooks.push(`${sample.name}: ${data.message}`);
                    }
                } catch (error) {
                    failedBooks.push(`${sample.name}: 网络错误`);
                }
            }

            if (successCount > 0) {
                showMessage(`成功添加 ${successCount} 个报备`, 'success');
                closeModal();
                // 重新加载列表
                component.loadReports();
                component.loadStatusCounts();
            }

            if (failedBooks.length > 0) {
                showMessage(`${failedBooks.length} 个报备添加失败`, 'error');
                console.error('失败的报备:', failedBooks);
            }
        }
    </script>
</body>
</html>
