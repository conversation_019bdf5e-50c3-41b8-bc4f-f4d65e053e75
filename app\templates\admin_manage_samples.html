<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员样书管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        .samples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }
        
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        .modal-content {
            z-index: 60;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .stat-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
        }
        
        .directory-item {
            transition: all 0.2s ease;
        }
        
        .directory-item:hover {
            background-color: #f8fafc;
            transform: translateX(4px);
        }
        
        .directory-item.active {
            background-color: #dbeafe;
            border-left: 4px solid #3b82f6;
        }
        
        .sample-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .sample-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #3b82f6;
        }
        
        .feature-tag {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: #0369a1;
            border: 1px solid #bae6fd;
        }
        
        .regulation-badge {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #fbbf24;
        }
        
        .award-badge {
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
            color: #be185d;
            border: 1px solid #f9a8d4;
        }
        
        .publisher-badge {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #166534;
            border: 1px solid #86efac;
        }

        /* 自定义搜索下拉框样式 */
        .custom-select {
            position: relative;
        }

        .custom-select-trigger {
            width: 100%;
            height: 40px;
            padding: 8px 40px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            background-color: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .custom-select-trigger:hover {
            border-color: #9ca3af;
        }

        .custom-select-trigger:focus,
        .custom-select.active .custom-select-trigger {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #6b7280;
            font-size: 12px;
        }

        .custom-select.active .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .custom-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 9999;
            max-height: 240px;
            overflow: hidden;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .custom-select.active .custom-select-dropdown {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .custom-select-search {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .custom-select-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 13px;
            outline: none;
            transition: border-color 0.2s ease;
        }

        .custom-select-search input:focus {
            border-color: #3b82f6;
        }

        .custom-select-options {
            max-height: 180px;
            overflow-y: auto;
        }

        .custom-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background-color 0.15s ease;
        }

        .custom-select-option:hover {
            background-color: #f3f4f6;
        }

        .custom-select-option.selected {
            background-color: #3b82f6;
            color: white;
        }

        .custom-select-option.no-results {
            color: #9ca3af;
            cursor: default;
            text-align: center;
            padding: 16px 12px;
        }

        /* 样书标签样式系统 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid;
            line-height: 1;
            gap: 0.25rem;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 学校层次标签 */
        .tag-level {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border-color: #81d4fa;
        }

        /* 图书类型标签 */
        .tag-book-type {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border-color: #a5d6a7;
        }

        /* 国家规划标签 */
        .tag-national {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            border-color: #ffcc02;
        }

        /* 省级规划标签 */
        .tag-provincial {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        /* 特色标签 */
        .tag-feature {
            background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);
            color: #33691e;
            border-color: #aed581;
        }

        /* 材质标签 */
        .tag-material {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            color: #ad1457;
            border-color: #f48fb1;
        }

        /* 色系标签 */
        .tag-color-colorful {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border-color: #fbbf24;
        }

        .tag-color-dual {
            background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
            color: #00695c;
            border-color: #4dd0e1;
        }

        .tag-color-four {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
            border-color: #ef5350;
        }

        /* 默认标签（备用） */
        .tag-default {
            background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
            color: #616161;
            border-color: #e0e0e0;
        }

        /* 视图切换按钮 */
        .view-toggle {
            background: #f1f5f9;
            border-radius: 0.75rem;
            padding: 0.25rem;
        }

        .view-toggle button {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .view-toggle button.active {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 列表视图样式 */
        .list-view {
            background: white;
            border-radius: 1rem;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .list-view table {
            width: 100%;
            border-collapse: collapse;
        }

        .list-view th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: #64748b;
            border-bottom: 1px solid #e2e8f0;
        }

        .list-view td {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: top;
        }

        .list-view tr:hover {
            background: #f8fafc;
        }

        .list-view tr:last-child td {
            border-bottom: none;
        }

        /* 排序表头样式 */
        .sort-header {
            background: none;
            border: none;
            padding: 0;
            font: inherit;
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            transition: color 0.2s ease;
        }

        .sort-header:hover {
            color: #475569;
        }

        .sort-icon {
            transition: all 0.2s ease;
        }

        .sort-icon.opacity-30 {
            opacity: 0.3;
        }

        .sort-icon.text-blue-600 {
            color: #2563eb;
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 主要内容区域 -->
    <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50" x-data="adminSampleManager()" x-init="initialize(); window.adminManagerInstance = $data">
        <div class="flex">
            <!-- 左侧筛选面板 -->
            <div class="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 min-h-screen">
                <!-- 筛选面板头部 -->
                <div class="p-6 border-b border-slate-200/60">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold text-slate-800">筛选条件</h2>
                        <button @click="resetAllFilters()"
                                class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                            重置
                        </button>
                    </div>

                    <!-- 模式切换 -->
                    <div class="flex bg-slate-100 rounded-lg p-1">
                        <button @click="switchMode('directory')"
                                :class="currentMode === 'directory' ? 'bg-white text-slate-800 shadow-sm' : 'text-slate-600'"
                                class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all">
                            目录浏览
                        </button>
                        <button @click="switchMode('filter')"
                                :class="currentMode === 'filter' ? 'bg-white text-slate-800 shadow-sm' : 'text-slate-600'"
                                class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all">
                            高级筛选
                        </button>
                    </div>
                </div>

                <!-- 筛选内容 -->
                <div class="overflow-y-auto custom-scrollbar" style="height: calc(100vh - 140px);">
                    <!-- 目录模式 -->
                    <template x-if="currentMode === 'directory'">
                        <div class="p-6 space-y-6">
                            <!-- 搜索框 -->
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-2">搜索样书</label>
                                <input type="text"
                                       x-model="searchKeyword"
                                       @input.debounce.500ms="loadSamples(1)"
                                       placeholder="输入书名、作者、ISBN进行搜索"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            </div>

                            <!-- 出版社选择器 -->
                            <div>
                                <h3 class="text-sm font-medium text-slate-700 mb-3">出版社选择</h3>
                                <div class="relative mb-4">
                                    <input type="text"
                                           x-model="publisherSearch"
                                           @input="searchPublishers()"
                                           placeholder="搜索出版社..."
                                           class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                    <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                                </div>

                                <!-- 出版社列表 -->
                                <div class="max-h-48 overflow-y-auto custom-scrollbar border border-slate-200 rounded-lg">
                                    <template x-if="publishers.loading">
                                        <div class="p-3 space-y-2">
                                            <template x-for="i in 3">
                                                <div class="loading-skeleton h-8 rounded"></div>
                                            </template>
                                        </div>
                                    </template>

                                    <template x-if="!publishers.loading">
                                        <div>
                                            <template x-for="publisher in publishers.list" :key="publisher.id">
                                                <div @click="selectPublisher(publisher.id, publisher.name)"
                                                     :class="{'bg-blue-50 border-blue-200': currentPublisherId === publisher.id}"
                                                     class="p-3 border-b border-slate-100 last:border-b-0 cursor-pointer hover:bg-slate-50 transition-colors">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-building text-blue-500 mr-2 text-sm"></i>
                                                            <span class="text-sm font-medium text-slate-700" x-text="publisher.name"></span>
                                                        </div>
                                                        <span class="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full"
                                                              x-text="publisher.sample_count + '本'"></span>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </template>

                                    <template x-if="!publishers.loading && publishers.list.length === 0">
                                        <div class="p-6 text-center text-slate-500">
                                            <i class="fas fa-search text-xl mb-2"></i>
                                            <p class="text-sm">未找到出版社</p>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <!-- 目录树 -->
                            <div x-show="currentPublisherId">
                                <h3 class="text-sm font-medium text-slate-700 mb-3">目录结构</h3>
                                <div class="border border-slate-200 rounded-lg">
                                    <!-- 全部样书选项 -->
                                    <div @click="selectDirectory(null, '全部样书')"
                                         :class="{'bg-blue-50 border-blue-200': currentDirectoryId === null}"
                                         class="p-3 border-b border-slate-100 cursor-pointer hover:bg-slate-50 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-list text-blue-500 mr-2 text-sm"></i>
                                                <span class="text-sm font-medium text-slate-700">全部样书</span>
                                            </div>
                                            <span class="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full"
                                                  x-text="directories.totalSamples + '本'"></span>
                                        </div>
                                    </div>

                                    <!-- 目录列表 -->
                                    <div class="max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-if="directories.loading">
                                            <div class="p-3 space-y-2">
                                                <template x-for="i in 3">
                                                    <div class="loading-skeleton h-8 rounded"></div>
                                                </template>
                                            </div>
                                        </template>

                                        <template x-if="!directories.loading">
                                            <div>
                                                <template x-for="directory in directories.tree" :key="directory.id">
                                                    <div @click="selectDirectory(directory.id, directory.name)"
                                                         :class="{'bg-blue-50 border-blue-200': currentDirectoryId === directory.id}"
                                                         class="p-3 border-b border-slate-100 last:border-b-0 cursor-pointer hover:bg-slate-50 transition-colors">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <i class="fas fa-folder text-yellow-500 mr-2 text-sm"></i>
                                                                <span class="text-sm font-medium text-slate-700" x-text="directory.name"></span>
                                                            </div>
                                                            <span class="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full"
                                                                  x-text="directory.sample_count + '本'"></span>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>

                                        <template x-if="!directories.loading && directories.tree.length === 0">
                                            <div class="p-6 text-center text-slate-500">
                                                <i class="fas fa-folder-open text-xl mb-2"></i>
                                                <p class="text-sm">暂无目录</p>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- 高级筛选模式 -->
                    <template x-if="currentMode === 'filter'">
                        <div class="flex flex-col h-full">
                            <!-- 可滑动的筛选项区域 -->
                            <div class="flex-1 overflow-y-auto custom-scrollbar p-6 space-y-4">
                                <!-- 搜索框 -->
                                <div>
                                    <label class="block text-sm font-medium text-slate-700 mb-2">关键词搜索</label>
                                    <input type="text"
                                           x-model="advancedFilters.search"
                                           @input.debounce.500ms="applyAdvancedFilters()"
                                           placeholder="搜索样书名称、作者、ISBN..."
                                           class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                </div>

                                <!-- 出版日期筛选 -->
                                <div class="border border-slate-200 rounded-lg overflow-hidden">
                                    <button type="button"
                                            @click="advancedFilters.publicationDateCollapsed = !advancedFilters.publicationDateCollapsed"
                                            class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                        <span class="text-sm font-medium text-slate-700">出版日期</span>
                                        <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                           :class="{'rotate-180': !advancedFilters.publicationDateCollapsed}"></i>
                                    </button>
                                    <div x-show="!advancedFilters.publicationDateCollapsed" x-transition class="p-3 border-t border-slate-200">
                                        <div class="space-y-2">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="radio" name="publication_date_filter" value="" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2" checked>
                                                <span class="ml-3 text-sm text-slate-700 font-medium">全部时间</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="radio" name="publication_date_filter" value="recent_three_years" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium">近三年</span>
                                            </label>
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="radio" name="publication_date_filter" value="custom" @change="handleCustomDateSelect()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium">自定义日期</span>
                                            </label>
                                            <!-- 自定义日期显示 -->
                                            <div x-show="advancedFilters.customDateRange.start && advancedFilters.customDateRange.end"
                                                 class="ml-7 text-xs text-slate-600 bg-blue-50 px-2 py-1 rounded">
                                                <span x-text="advancedFilters.customDateRange.start + ' 至 ' + advancedFilters.customDateRange.end"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 学校层次 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.levelCollapsed = !advancedFilters.levelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">学校层次</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.levelCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.levelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="level in filterOptions.levels" :key="level.id">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="level.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- 图书类型 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.typeCollapsed = !advancedFilters.typeCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">图书类型</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.typeCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.typeCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="type in filterOptions.types" :key="type.id">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="type.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="type.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- 规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.rankCollapsed = !advancedFilters.rankCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.rankCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.rankCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                            <input type="checkbox" value="国家规划" @change="applyAdvancedFilters()"
                                                   class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                            <span class="ml-3 text-sm text-slate-700 font-medium">国家规划</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                            <input type="checkbox" value="省级规划" @change="applyAdvancedFilters()"
                                                   class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                            <span class="ml-3 text-sm text-slate-700 font-medium">省级规划</span>
                                        </label>
                                        <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                            <input type="checkbox" value="普通教材" @change="applyAdvancedFilters()"
                                                   class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                            <span class="ml-3 text-sm text-slate-700 font-medium">普通教材</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 国家规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.nationalLevelCollapsed = !advancedFilters.nationalLevelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">国家规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.nationalLevelCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.nationalLevelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="level in filterOptions.nationalLevels" :key="level.id">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="level.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- 省级规划级别 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.provincialLevelCollapsed = !advancedFilters.provincialLevelCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">省级规划级别</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.provincialLevelCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.provincialLevelCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="level in filterOptions.provincialLevels" :key="level.id">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="level.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="level.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- 出版社筛选 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.publisherCollapsed = !advancedFilters.publisherCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">出版社</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.publisherCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.publisherCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="mb-3">
                                        <input type="text"
                                               x-model="advancedFilters.publisherSearch"
                                               placeholder="搜索出版社..."
                                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                    </div>
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="publisher in filteredPublishers" :key="publisher.name">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="publisher.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="publisher.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- 特色标签 -->
                            <div class="border border-slate-200 rounded-lg overflow-hidden">
                                <button type="button"
                                        @click="advancedFilters.featureCollapsed = !advancedFilters.featureCollapsed"
                                        class="w-full px-3 py-3 text-left flex justify-between items-center hover:bg-slate-50 bg-slate-50">
                                    <span class="text-sm font-medium text-slate-700">特色标签</span>
                                    <i class="fas fa-chevron-down text-sm transition-transform duration-200"
                                       :class="{'rotate-180': !advancedFilters.featureCollapsed}"></i>
                                </button>
                                <div x-show="!advancedFilters.featureCollapsed" x-transition class="p-3 border-t border-slate-200">
                                    <div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar">
                                        <template x-for="feature in filterOptions.features" :key="feature.id">
                                            <label class="flex items-center cursor-pointer p-2 rounded-lg hover:bg-slate-50 transition-colors">
                                                <input type="checkbox" :value="feature.name" @change="applyAdvancedFilters()"
                                                       class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500 focus:ring-2">
                                                <span class="ml-3 text-sm text-slate-700 font-medium" x-text="feature.name"></span>
                                            </label>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <!-- 固定在底部的重置按钮 -->
                            <div class="p-6 border-t border-slate-200 bg-white">
                                <button @click="resetAdvancedFilters()"
                                        class="w-full px-4 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md">
                                    <i class="fas fa-redo mr-2"></i>重置筛选
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- 右侧主内容区 -->
            <div class="flex-1 flex flex-col bg-white/60 backdrop-blur-sm">
                <!-- 顶部操作栏 -->
                <div class="flex justify-between items-center p-6 border-b border-slate-200/60">
                    <!-- 左侧管理按钮 -->
                    <div class="flex items-center space-x-4">
                        <button onclick="window.location.href='/pc_admin_manage_sample_fields'"
                                class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                            <i class="fas fa-cogs"></i>
                            <span>字段管理</span>
                        </button>
                        <button onclick="window.location.href='/admin_price_changes'"
                                class="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-sm">
                            <i class="fas fa-chart-line"></i>
                            <span>价格管理</span>
                        </button>
                    </div>

                    <!-- 右侧操作按钮 -->
                    <div class="flex items-center space-x-4">
                        <!-- 视图切换 -->
                        <div class="view-toggle flex">
                            <button @click="viewMode = 'grid'"
                                    :class="{'active': viewMode === 'grid'}"
                                    class="flex items-center gap-2">
                                <i class="fas fa-th"></i>
                                <span class="hidden sm:inline">网格</span>
                            </button>
                            <button @click="viewMode = 'list'"
                                    :class="{'active': viewMode === 'list'}"
                                    class="flex items-center gap-2">
                                <i class="fas fa-list"></i>
                                <span class="hidden sm:inline">列表</span>
                            </button>
                        </div>

                        <!-- 操作按钮组 -->
                        <div class="flex items-center space-x-3">
                            <button @click="exportSamples()"
                                    class="btn-primary h-12 px-6 text-white rounded-xl flex items-center space-x-2 shadow-lg">
                                <i class="fas fa-download"></i>
                                <span>导出数据</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 样书列表区域 -->
                <div class="flex-1 overflow-y-auto custom-scrollbar p-6">
                    <!-- 加载状态 -->
                    <template x-if="samples.loading">
                        <div class="samples-grid">
                            <template x-for="i in 8">
                                <div class="bg-white rounded-2xl p-6 shadow-sm h-96">
                                    <div class="loading-skeleton h-6 w-3/4 rounded mb-4"></div>
                                    <div class="loading-skeleton h-4 w-1/2 rounded mb-3"></div>
                                    <div class="loading-skeleton h-4 w-2/3 rounded mb-3"></div>
                                    <div class="space-y-2">
                                        <div class="loading-skeleton h-3 w-full rounded"></div>
                                        <div class="loading-skeleton h-3 w-full rounded"></div>
                                        <div class="loading-skeleton h-3 w-3/4 rounded"></div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>

                    <!-- 样书网格视图 -->
                    <template x-if="!samples.loading && samples.list.length > 0 && viewMode === 'grid'">
                        <div class="samples-grid">
                            <template x-for="sample in samples.list" :key="sample.id">
                                <article class="sample-card bg-white rounded-2xl shadow-sm border border-slate-100 overflow-hidden hover:border-slate-200">
                                    <div class="sample-card-content">
                                        <!-- 卡片头部 -->
                                        <div class="p-6 pb-4">
                                            <div class="flex justify-between items-start mb-3">
                                                <div class="flex-1 pr-3">
                                                    <h3 class="text-lg font-semibold text-slate-800 line-clamp-2 leading-tight mb-2"
                                                        :title="sample.name" x-text="sample.name"></h3>
                                                    <div class="space-y-1">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-user text-slate-400 text-xs mr-2"></i>
                                                            <span class="text-sm text-slate-500" x-text="sample.author || '未填写'"></span>
                                                        </div>
                                                        <div class="flex items-center">
                                                            <i class="fas fa-building text-slate-400 text-xs mr-2"></i>
                                                            <span class="text-sm text-slate-600" x-text="sample.publisher_name || '未设置'"></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 操作菜单 -->
                                                <div class="relative" x-data="{ open: false }">
                                                    <button @click="open = !open"
                                                            class="w-8 h-8 bg-slate-50 hover:bg-slate-100 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                                    </button>

                                                    <div x-show="open"
                                                         @click.away="open = false"
                                                         x-transition:enter="transition ease-out duration-200"
                                                         x-transition:enter-start="opacity-0 scale-95"
                                                         x-transition:enter-end="opacity-1 scale-100"
                                                         x-transition:leave="transition ease-in duration-75"
                                                         x-transition:leave-start="opacity-1 scale-100"
                                                         x-transition:leave-end="opacity-0 scale-95"
                                                         class="absolute right-0 top-10 w-40 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                                                        <button @click="viewSampleDetail(sample.id); open = false"
                                                                class="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-blue-50 hover:text-blue-700 flex items-center space-x-2 transition-colors rounded-lg">
                                                            <i class="fas fa-eye text-blue-500"></i>
                                                            <span>查看详情</span>
                                                        </button>
                                                        <button @click="editSample(sample.id); open = false"
                                                                class="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-green-50 hover:text-green-700 flex items-center space-x-2 transition-colors rounded-lg">
                                                            <i class="fas fa-edit text-green-500"></i>
                                                            <span>编辑样书</span>
                                                        </button>
                                                        <button @click="deleteSample(sample.id, sample.name); open = false"
                                                                class="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 hover:text-red-700 flex items-center space-x-2 transition-colors rounded-lg">
                                                            <i class="fas fa-trash-alt text-red-500"></i>
                                                            <span>删除样书</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- ISBN 和价格 -->
                                            <div class="flex items-center justify-between mb-4 p-3 bg-slate-50 rounded-xl">
                                                <div class="text-sm">
                                                    <span class="text-slate-500">ISBN:</span>
                                                    <span class="text-slate-700 font-mono ml-1" x-text="sample.isbn"></span>
                                                </div>
                                                <div class="text-lg font-bold text-blue-600"
                                                     x-text="sample.price ? `¥${parseFloat(sample.price).toFixed(2)}` : '未定价'"></div>
                                            </div>
                                        </div>

                                        <!-- 卡片主体内容 -->
                                        <div class="sample-card-body px-6 pb-4">
                                            <!-- 折扣信息 -->
                                            <div class="grid grid-cols-3 gap-3 mb-4">
                                                <div class="text-center p-3 bg-blue-50 rounded-lg">
                                                    <div class="text-xs text-blue-600 font-medium mb-1">发货折扣</div>
                                                    <div class="text-sm font-bold text-blue-700"
                                                         x-text="sample.shipping_discount ? (sample.shipping_discount * 100).toFixed(0) + '%' : '无'"></div>
                                                </div>
                                                <div class="text-center p-3 bg-green-50 rounded-lg">
                                                    <div class="text-xs text-green-600 font-medium mb-1">结算折扣</div>
                                                    <div class="text-sm font-bold text-green-700"
                                                         x-text="sample.settlement_discount ? (sample.settlement_discount * 100).toFixed(0) + '%' : '无'"></div>
                                                </div>
                                                <div class="text-center p-3 bg-purple-50 rounded-lg">
                                                    <div class="text-xs text-purple-600 font-medium mb-1">推广费率</div>
                                                    <div class="text-sm font-bold text-purple-700"
                                                         x-text="sample.promotion_rate ?
                                                                 (sample.promotion_rate * 100).toFixed(0) + '%' :
                                                                 (sample.shipping_discount && sample.settlement_discount ?
                                                                     ((sample.shipping_discount - sample.settlement_discount) * 100).toFixed(0) + '%' :
                                                                     '无')"></div>
                                                    <div class="text-xs text-purple-500"
                                                         x-text="sample.promotion_rate ? '用户填写' : '系统计算'"></div>
                                                </div>
                                            </div>

                                            <!-- 属性标签 -->
                                            <div class="space-y-3">
                                                <div class="flex flex-wrap gap-2">
                                                    <template x-if="sample.level">
                                                        <span class="tag tag-level">
                                                            <i class="fas fa-graduation-cap"></i>
                                                            <span x-text="sample.level"></span>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.book_type">
                                                        <span class="tag tag-book-type">
                                                            <i class="fas fa-book"></i>
                                                            <span x-text="sample.book_type"></span>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.material_type">
                                                        <span class="tag tag-material">
                                                            <i class="fas fa-file-alt"></i>
                                                            <span x-text="sample.material_type"></span>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.color_system">
                                                        <span class="tag"
                                                              :class="sample.color_system === '彩色' ? 'tag-color-colorful' :
                                                                      sample.color_system === '双色' ? 'tag-color-dual' :
                                                                      sample.color_system === '四色' ? 'tag-color-four' : 'tag-default'">
                                                            <i class="fas fa-palette"></i>
                                                            <span x-text="sample.color_system"></span>
                                                        </span>
                                                    </template>
                                                </div>

                                                <div class="flex flex-wrap gap-2">
                                                    <template x-if="sample.national_regulation == 1">
                                                        <span class="tag tag-national">
                                                            <i class="fas fa-star"></i>
                                                            <span>国家规划</span>
                                                            <template x-if="sample.national_regulation_level_name">
                                                                <span x-text="'(' + sample.national_regulation_level_name + ')'"></span>
                                                            </template>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.provincial_regulation == 1">
                                                        <span class="tag tag-provincial">
                                                            <i class="fas fa-medal"></i>
                                                            <span>省级规划</span>
                                                            <template x-if="sample.provincial_regulation_level_name">
                                                                <span x-text="'(' + sample.provincial_regulation_level_name + ')'"></span>
                                                            </template>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.feature_name">
                                                        <span class="tag tag-feature">
                                                            <i class="fas fa-tags"></i>
                                                            <span x-text="sample.feature_name"></span>
                                                        </span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </template>
                        </div>
                    </template>

                    <!-- 样书列表视图 -->
                    <template x-if="!samples.loading && samples.list.length > 0 && viewMode === 'list'">
                        <div class="list-view">
                            <table>
                                <thead>
                                    <tr>
                                        <th style="width: 280px;">
                                            <button class="sort-header" @click="handleSort('name')" data-field="name">
                                                <span>样书信息</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'name' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'name' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 100px;">
                                            <button class="sort-header" @click="handleSort('price')" data-field="price">
                                                <span>价格</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'price' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'price' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 120px;">
                                            <button class="sort-header" @click="handleSort('publisher_name')" data-field="publisher_name">
                                                <span>出版社</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'publisher_name' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'publisher_name' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 180px;">标签</th>
                                        <th style="width: 100px;">
                                            <button class="sort-header" @click="handleSort('shipping_discount')" data-field="shipping_discount">
                                                <span>发货折扣</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'shipping_discount' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'shipping_discount' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 100px;">
                                            <button class="sort-header" @click="handleSort('settlement_discount')" data-field="settlement_discount">
                                                <span>结算折扣</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'settlement_discount' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'settlement_discount' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 100px;">
                                            <button class="sort-header" @click="handleSort('promotion_rate')" data-field="promotion_rate">
                                                <span>推广费率</span>
                                                <div class="flex flex-col">
                                                    <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'promotion_rate' && sortOrder === 'asc'}"></i>
                                                    <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"
                                                       :class="{'text-blue-600 opacity-100': sortField === 'promotion_rate' && sortOrder === 'desc'}"></i>
                                                </div>
                                            </button>
                                        </th>
                                        <th style="width: 120px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template x-for="sample in samples.list" :key="sample.id">
                                        <tr>
                                            <!-- 样书信息 -->
                                            <td>
                                                <div class="flex items-start space-x-3">
                                                    <!-- 封面缩略图 -->
                                                    <div class="w-12 h-16 bg-slate-100 rounded flex-shrink-0 overflow-hidden">
                                                        <template x-if="sample.attachment_link">
                                                            <img :src="sample.attachment_link"
                                                                 :alt="sample.name"
                                                                 class="w-full h-full object-cover cursor-pointer"
                                                                 @click="showCoverModal(sample.attachment_link)">
                                                        </template>
                                                        <template x-if="!sample.attachment_link">
                                                            <div class="w-full h-full flex items-center justify-center">
                                                                <i class="fas fa-book text-slate-400 text-sm"></i>
                                                            </div>
                                                        </template>
                                                    </div>
                                                    <!-- 样书详情 -->
                                                    <div class="flex-1 min-w-0">
                                                        <h3 class="font-semibold text-slate-800 text-sm line-clamp-2 mb-1"
                                                            :title="sample.name" x-text="sample.name"></h3>
                                                        <div class="text-xs text-slate-600 space-y-1">
                                                            <div x-show="sample.author">
                                                                <span class="font-medium">作者：</span>
                                                                <span x-text="sample.author"></span>
                                                            </div>
                                                            <div x-show="sample.isbn">
                                                                <span class="font-medium">ISBN：</span>
                                                                <span x-text="sample.isbn"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- 价格 -->
                                            <td>
                                                <div class="text-lg font-bold text-blue-600" x-text="'¥' + (sample.price || '0.00')"></div>
                                            </td>

                                            <!-- 出版社 -->
                                            <td>
                                                <div class="text-sm font-medium text-slate-800" x-text="sample.publisher_name || '未知'"></div>
                                            </td>

                                            <!-- 标签 -->
                                            <td>
                                                <div class="flex flex-wrap gap-1">
                                                    <template x-if="sample.level">
                                                        <span class="tag tag-level text-xs">
                                                            <i class="fas fa-graduation-cap"></i>
                                                            <span x-text="sample.level"></span>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.book_type">
                                                        <span class="tag tag-book-type text-xs">
                                                            <i class="fas fa-book"></i>
                                                            <span x-text="sample.book_type"></span>
                                                        </span>
                                                    </template>
                                                    <template x-if="sample.national_regulation == 1">
                                                        <span class="tag tag-national text-xs">
                                                            <i class="fas fa-star"></i>
                                                            <span>国家规划</span>
                                                        </span>
                                                    </template>
                                                </div>
                                            </td>

                                            <!-- 发货折扣 -->
                                            <td>
                                                <div class="text-sm font-medium text-slate-800 text-center">
                                                    <span x-text="(sample.shipping_discount ? (parseFloat(sample.shipping_discount) * 100).toFixed(1) : '0.0') + '%'"></span>
                                                </div>
                                            </td>

                                            <!-- 结算折扣 -->
                                            <td>
                                                <div class="text-sm font-medium text-slate-800 text-center">
                                                    <span x-text="(sample.settlement_discount ? (parseFloat(sample.settlement_discount) * 100).toFixed(1) : '0.0') + '%'"></span>
                                                </div>
                                            </td>

                                            <!-- 推广费率 -->
                                            <td>
                                                <div class="text-center">
                                                    <template x-if="sample.promotion_rate !== null && sample.promotion_rate !== undefined">
                                                        <div>
                                                            <div class="text-sm font-medium text-blue-600" x-text="(parseFloat(sample.promotion_rate) * 100).toFixed(1) + '%'"></div>
                                                            <div class="text-xs text-slate-500" x-text="sample.promotion_rate_source === 'user' ? '用户设置' : '系统计算'"></div>
                                                        </div>
                                                    </template>
                                                    <template x-if="sample.promotion_rate === null || sample.promotion_rate === undefined">
                                                        <span class="text-slate-400 text-sm">未设置</span>
                                                    </template>
                                                </div>
                                            </td>

                                            <!-- 操作 -->
                                            <td>
                                                <div class="flex items-center space-x-2">
                                                    <button @click="viewSampleDetail(sample.id)"
                                                            class="text-blue-600 hover:text-blue-800 text-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button @click="editSample(sample)"
                                                            class="text-green-600 hover:text-green-800 text-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button @click="deleteSample(sample.id)"
                                                            class="text-red-600 hover:text-red-800 text-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </template>

                    <!-- 空状态页面 -->
                    <template x-if="!samples.loading && samples.list.length === 0">
                        <div class="flex flex-col items-center justify-center py-20 text-center">
                            <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
                                <i class="fas fa-book text-slate-400 text-3xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-slate-700 mb-3">
                                <span x-show="currentMode === 'directory'">该目录下暂无样书</span>
                                <span x-show="currentMode === 'filter'">无符合条件的书籍</span>
                            </h3>
                            <p class="text-slate-500 mb-8 max-w-md">
                                <span x-show="currentMode === 'directory'">当前目录下还没有样书，请选择其他目录或出版社</span>
                                <span x-show="currentMode === 'filter'">请尝试调整筛选条件或重置筛选器</span>
                            </p>
                        </div>
                    </template>

                    <!-- 分页控件 -->
                    <template x-if="!samples.loading && samples.list.length > 0 && samples.totalPages > 1">
                        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
                            <!-- 信息显示区域 -->
                            <div class="flex items-center">
                                <p class="text-sm text-gray-700 mr-4">
                                    第 <span class="font-medium" x-text="samples.currentPage"></span> 页，
                                    共 <span class="font-medium" x-text="samples.totalPages"></span> 页，
                                    共 <span class="font-medium" x-text="samples.total || 0"></span> 条
                                </p>
                            </div>

                            <!-- 分页按钮区域 -->
                            <div class="flex gap-1">
                                <!-- 首页按钮 -->
                                <button @click="goToPage(1)"
                                        :disabled="samples.currentPage <= 1"
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">首页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <!-- 上一页按钮 -->
                                <button @click="goToPage(samples.currentPage - 1)"
                                        :disabled="samples.currentPage <= 1"
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">上一页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <!-- 页码按钮容器 -->
                                <div id="adminPageNumbers" class="flex gap-1"></div>

                                <!-- 下一页按钮 -->
                                <button @click="goToPage(samples.currentPage + 1)"
                                        :disabled="samples.currentPage >= samples.totalPages"
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">下一页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <!-- 末页按钮 -->
                                <button @click="goToPage(samples.totalPages)"
                                        :disabled="samples.currentPage >= samples.totalPages"
                                        class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span class="sr-only">末页</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0zm-6 0a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer" class="fixed inset-0 hidden">
        <!-- 遮罩层 -->
        <div class="modal-overlay" onclick="closeModal()"></div>
        <!-- 模态框内容 -->
        <div class="modal-content fixed inset-0 flex items-center justify-center p-4 pointer-events-none">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col pointer-events-auto">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200 flex-shrink-0">
                    <h3 id="modalTitle" class="text-xl font-semibold text-slate-800"></h3>
                    <button onclick="closeModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容区域（可滚动） -->
                <div id="modalBody" class="p-6 overflow-y-auto flex-1 custom-scrollbar"></div>
                <!-- 模态框底部按钮区域（固定） -->
                <div id="modalFooter" class="p-6 border-t border-slate-200 flex-shrink-0" style="display: none;">
                    <!-- 按钮将通过JavaScript动态插入 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        // 全局函数
        function openModal(title, content, footer = null) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;

            const modalFooter = document.getElementById('modalFooter');
            if (footer) {
                modalFooter.innerHTML = footer;
                modalFooter.style.display = 'block';
            } else {
                modalFooter.style.display = 'none';
            }

            document.getElementById('modalContainer').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('modalContainer').classList.add('hidden');
        }

        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');

            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';

            messageDiv.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            // 显示动画
            setTimeout(() => {
                messageDiv.classList.remove('translate-x-full', 'opacity-0');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                messageDiv.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (container.contains(messageDiv)) {
                        container.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 全局函数用于模态框中的按钮调用
        function confirmEditSampleGlobal() {
            const element = document.querySelector('[x-data]');
            if (element && element._x_dataStack && element._x_dataStack[0]) {
                element._x_dataStack[0].confirmEditSample();
            } else if (element && element.__x && element.__x.$data) {
                element.__x.$data.confirmEditSample();
            } else {
                showMessage('系统错误，请刷新页面重试', 'error');
            }
        }

        // 显示封面放大模态框
        function showCoverModal(imageUrl, title) {
            // 创建独立的封面模态框
            const coverModalHtml = `
                <div id="coverModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]" onclick="closeCoverModal()">
                    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl max-h-[90vh] overflow-auto m-4" onclick="event.stopPropagation()">
                        <div class="p-6">
                            <div class="flex justify-end mb-4">
                                <button onclick="closeCoverModal()" class="text-slate-400 hover:text-slate-600 text-xl">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="text-center">
                                <div class="mb-4">
                                    <img src="${imageUrl}"
                                         alt="${title}"
                                         class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none;" class="max-w-full max-h-96 mx-auto flex items-center justify-center bg-slate-100 rounded-lg border border-slate-200 p-8">
                                        <div class="text-center text-slate-500">
                                            <i class="fas fa-image text-4xl mb-2"></i>
                                            <div class="text-lg">封面加载失败</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end mt-6">
                                <button onclick="closeCoverModal()"
                                        class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', coverModalHtml);
        }

        // 关闭封面模态框
        function closeCoverModal() {
            const coverModal = document.getElementById('coverModalContainer');
            if (coverModal) {
                coverModal.remove();
            }
        }

        // Alpine.js 组件
        function adminSampleManager() {
            return {
                // 状态管理
                currentMode: 'directory', // 'directory' 或 'filter'
                currentPublisherId: null,
                currentPublisherName: '',
                currentDirectoryId: null,
                currentDirectoryName: '全部样书',
                searchKeyword: '',
                publisherSearch: '',
                viewMode: 'grid', // 'grid' 或 'list'

                // 出版社数据
                publishers: {
                    list: [],
                    loading: false,
                    allList: [] // 保存所有出版社用于搜索
                },

                // 目录数据
                directories: {
                    tree: [],
                    loading: false,
                    totalSamples: 0
                },

                // 样书数据
                samples: {
                    list: [],
                    allSamples: [], // 存储所有筛选后的数据用于前端排序
                    loading: false,
                    currentPage: 1,
                    totalPages: 1,
                    total: 0,
                    pageSize: 12
                },

                // 排序相关
                sortField: '',
                sortOrder: '', // 'asc', 'desc', ''
                sortedSamples: [], // 存储排序后的数据

                // 高级筛选
                advancedFilters: {
                    search: '',
                    publisherSearch: '',
                    levelCollapsed: true,
                    typeCollapsed: true,
                    rankCollapsed: true,
                    nationalLevelCollapsed: true,
                    provincialLevelCollapsed: true,
                    publisherCollapsed: true,
                    featureCollapsed: true,
                    publicationDateCollapsed: false,
                    publicationDateFilter: '', // '', 'recent_three_years', 'custom'
                    customDateRange: {
                        start: '',
                        end: ''
                    }
                },

                // 筛选选项
                filterOptions: {
                    levels: [],
                    types: [],
                    nationalLevels: [],
                    provincialLevels: [],
                    publishers: [],
                    features: []
                },

                // 计算属性
                get filteredPublishers() {
                    if (!Array.isArray(this.filterOptions.publishers)) {
                        return [];
                    }

                    if (!this.advancedFilters.publisherSearch || this.advancedFilters.publisherSearch.trim() === '') {
                        return this.filterOptions.publishers;
                    }

                    const searchTerm = this.advancedFilters.publisherSearch.toLowerCase().trim();
                    return this.filterOptions.publishers.filter(publisher =>
                        publisher.name && publisher.name.toLowerCase().includes(searchTerm)
                    );
                },

                // 初始化
                async initialize() {
                    await this.loadPublishers();
                    await this.loadFilterOptions();
                },

                // 模式切换
                switchMode(mode) {
                    this.currentMode = mode;
                    if (mode === 'directory') {
                        this.currentDirectoryName = this.currentDirectoryId ? '目录样书' : '全部样书';
                        this.loadSamples(1);
                    } else {
                        this.currentDirectoryName = '高级筛选';
                        this.applyAdvancedFilters();
                    }
                },

                // 重置所有筛选
                resetAllFilters() {
                    this.currentPublisherId = null;
                    this.currentPublisherName = '';
                    this.currentDirectoryId = null;
                    this.currentDirectoryName = '全部样书';
                    this.searchKeyword = '';
                    this.publisherSearch = '';

                    // 重置排序状态
                    this.sortField = '';
                    this.sortOrder = '';

                    this.resetAdvancedFilters();
                    this.switchMode('directory');
                },

                // 加载筛选选项
                async loadFilterOptions() {
                    try {
                        // 加载所有筛选选项
                        const response = await $.ajax({
                            url: '/api/admin/get_filter_options',
                            type: 'GET'
                        });

                        if (response.code === 0) {
                            this.filterOptions.levels = response.data.levels || [];
                            this.filterOptions.types = response.data.types || [];
                            this.filterOptions.nationalLevels = response.data.national_levels || [];
                            this.filterOptions.provincialLevels = response.data.provincial_levels || [];
                            this.filterOptions.publishers = response.data.publishers || [];
                            this.filterOptions.features = response.data.features || [];
                        }
                    } catch (error) {
                        console.error('加载筛选选项失败:', error);
                    }
                },

                // 应用高级筛选
                async applyAdvancedFilters() {
                    if (this.currentMode !== 'filter') return;

                    this.samples.loading = true;

                    try {
                        // 收集筛选条件
                        const filterData = this.collectFilterData();

                        // 收集出版日期筛选参数
                        let publicationDateFilter = '';
                        let publicationStartDate = '';
                        let publicationEndDate = '';

                        const selectedDateFilter = document.querySelector('input[name="publication_date_filter"]:checked');
                        if (selectedDateFilter) {
                            publicationDateFilter = selectedDateFilter.value;
                            if (publicationDateFilter === 'custom') {
                                publicationStartDate = this.advancedFilters.customDateRange.start;
                                publicationEndDate = this.advancedFilters.customDateRange.end;
                            }
                        }

                        const response = await $.ajax({
                            url: '/api/admin/filter_admin_samples',
                            type: 'GET',
                            data: {
                                search: this.advancedFilters.search,
                                levels: JSON.stringify(filterData.levels),
                                types: JSON.stringify(filterData.types),
                                ranks: JSON.stringify(filterData.ranks),
                                national_levels: JSON.stringify(filterData.nationalLevels),
                                provincial_levels: JSON.stringify(filterData.provincialLevels),
                                publishers: JSON.stringify(filterData.publishers),
                                features: JSON.stringify(filterData.features),
                                publication_date_filter: publicationDateFilter,
                                publication_start_date: publicationStartDate,
                                publication_end_date: publicationEndDate,
                                page: 1,
                                limit: 10000 // 加载所有筛选结果用于前端排序
                            }
                        });

                        if (response.code === 0) {
                            // 存储所有筛选后的数据
                            this.samples.allSamples = response.data.samples || [];
                            this.samples.total = this.samples.allSamples.length;
                            this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);

                            // 重置排序状态
                            this.sortField = '';
                            this.sortOrder = '';
                            this.sortedSamples = [...this.samples.allSamples];

                            // 设置当前页数据
                            this.updateCurrentPageData();

                            // 渲染分页按钮
                            this.$nextTick(() => {
                                this.renderPageNumbers();
                            });
                        } else {
                            showMessage('筛选失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.samples.loading = false;
                    }
                },

                // 收集筛选数据
                collectFilterData() {
                    const levels = [];
                    const types = [];
                    const ranks = [];
                    const nationalLevels = [];
                    const provincialLevels = [];
                    const publishers = [];
                    const features = [];

                    // 收集所有选中的复选框
                    document.querySelectorAll('input[type="checkbox"][value]').forEach(checkbox => {
                        if (checkbox.checked) {
                            const value = checkbox.value;

                            // 根据值判断属于哪个筛选类别
                            if (this.filterOptions.levels.some(l => l.name === value)) {
                                levels.push(value);
                            } else if (this.filterOptions.types.some(t => t.name === value)) {
                                types.push(value);
                            } else if (['国家规划', '省级规划', '普通教材'].includes(value)) {
                                ranks.push(value);
                            } else if (this.filterOptions.nationalLevels.some(nl => nl.name === value)) {
                                nationalLevels.push(value);
                            } else if (this.filterOptions.provincialLevels.some(pl => pl.name === value)) {
                                provincialLevels.push(value);
                            } else if (this.filterOptions.publishers.some(p => p.name === value)) {
                                publishers.push(value);
                            } else if (this.filterOptions.features.some(f => f.name === value)) {
                                features.push(value);
                            }
                        }
                    });

                    return { levels, types, ranks, nationalLevels, provincialLevels, publishers, features };
                },

                // 重置高级筛选
                resetAdvancedFilters() {
                    this.advancedFilters.search = '';
                    this.advancedFilters.publisherSearch = '';

                    // 取消所有复选框选中状态
                    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // 重置出版日期筛选
                    document.querySelector('input[name="publication_date_filter"][value=""]').checked = true;
                    this.advancedFilters.publicationDateFilter = '';
                    this.advancedFilters.customDateRange.start = '';
                    this.advancedFilters.customDateRange.end = '';

                    // 重置排序状态
                    this.sortField = '';
                    this.sortOrder = '';

                    if (this.currentMode === 'filter') {
                        this.applyAdvancedFilters();
                    }
                },

                // 页码生成函数
                getPageNumbers(currentPage, totalPages) {
                    const pageNumbers = [];

                    if (totalPages <= 7) {
                        // 总页数不超过7页，显示所有页码
                        for (let i = 1; i <= totalPages; i++) {
                            pageNumbers.push(i);
                        }
                    } else {
                        // 总页数超过7页，使用省略号
                        pageNumbers.push(1);

                        if (currentPage <= 4) {
                            // 当前页在前部
                            pageNumbers.push(2, 3, 4, 5);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        } else if (currentPage >= totalPages - 3) {
                            // 当前页在后部
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                            pageNumbers.push(totalPages);
                        } else {
                            // 当前页在中部
                            pageNumbers.push('...');
                            pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                            pageNumbers.push('...');
                            pageNumbers.push(totalPages);
                        }
                    }

                    return pageNumbers;
                },

                // 渲染页码按钮
                renderPageNumbers() {
                    const container = document.getElementById('adminPageNumbers');
                    if (!container) return;

                    container.innerHTML = '';

                    const pageNumbers = this.getPageNumbers(this.samples.currentPage, this.samples.totalPages);

                    pageNumbers.forEach(pageNumber => {
                        if (pageNumber === '...') {
                            // 省略号
                            container.innerHTML += `
                                <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                                    ...
                                </span>
                            `;
                        } else {
                            // 页码按钮
                            const isActive = pageNumber === this.samples.currentPage;
                            const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                            container.innerHTML += `
                                <button data-page="${pageNumber}"
                                        class="admin-page-number-btn relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                                    ${pageNumber}
                                </button>
                            `;
                        }
                    });

                    // 绑定页码按钮点击事件
                    container.querySelectorAll('.admin-page-number-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            const page = parseInt(e.target.dataset.page);
                            this.goToPage(page);
                        });
                    });
                },

                // 加载出版社列表
                async loadPublishers() {
                    this.publishers.loading = true;
                    try {
                        const response = await $.ajax({
                            url: '/api/admin/get_sample_publishers',
                            type: 'GET'
                        });

                        if (response.code === 0) {
                            this.publishers.allList = response.data;
                            this.publishers.list = response.data;
                        } else {
                            showMessage('获取出版社列表失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.publishers.loading = false;
                    }
                },

                // 搜索出版社
                searchPublishers() {
                    const keyword = this.publisherSearch.toLowerCase();
                    if (keyword) {
                        this.publishers.list = this.publishers.allList.filter(publisher =>
                            publisher.name.toLowerCase().includes(keyword)
                        );
                    } else {
                        this.publishers.list = this.publishers.allList;
                    }
                },

                // 选择出版社
                async selectPublisher(publisherId, publisherName) {
                    this.currentPublisherId = publisherId;
                    this.currentPublisherName = publisherName;
                    this.currentDirectoryId = null;
                    this.currentDirectoryName = '全部样书';

                    await this.loadDirectories();
                    await this.loadSamples(1);
                },

                // 加载目录
                async loadDirectories() {
                    if (!this.currentPublisherId) return;

                    this.directories.loading = true;
                    try {
                        const response = await $.ajax({
                            url: '/api/admin/get_sample_directories',
                            type: 'GET',
                            data: { publisher_id: this.currentPublisherId }
                        });

                        if (response.code === 0) {
                            this.directories.tree = this.buildDirectoryTree(response.data);
                            this.directories.totalSamples = response.total_samples || 0;
                        } else {
                            showMessage('获取目录失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.directories.loading = false;
                    }
                },

                // 构建目录树
                buildDirectoryTree(directories, parentId = null, level = 0) {
                    const children = directories.filter(dir => dir.parent_id === parentId);
                    let result = [];

                    children.forEach(dir => {
                        dir.level = level;
                        result.push(dir);
                        const subChildren = this.buildDirectoryTree(directories, dir.id, level + 1);
                        result = result.concat(subChildren);
                    });

                    return result;
                },

                // 选择目录
                async selectDirectory(dirId, dirName) {
                    this.currentDirectoryId = dirId;
                    this.currentDirectoryName = dirName;
                    await this.loadSamples(1);
                },

                // 加载样书列表
                async loadSamples(page = 1) {
                    if (!this.currentPublisherId) return;

                    this.samples.loading = true;
                    this.samples.currentPage = page;

                    try {
                        let data = {
                            publisher_id: this.currentPublisherId,
                            page: 1,
                            limit: 10000 // 加载所有数据用于前端排序
                        };

                        if (this.currentDirectoryId) {
                            data.directory_id = this.currentDirectoryId;
                        }
                        if (this.searchKeyword) {
                            data.search = this.searchKeyword;
                        }

                        const response = await $.ajax({
                            url: '/api/admin/get_admin_samples',
                            type: 'GET',
                            data: data
                        });

                        if (response.code === 0) {
                            // 存储所有数据
                            this.samples.allSamples = response.data.samples || [];
                            this.samples.total = this.samples.allSamples.length;
                            this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);

                            // 重置排序状态
                            this.sortField = '';
                            this.sortOrder = '';
                            this.sortedSamples = [...this.samples.allSamples];

                            // 设置当前页数据
                            this.updateCurrentPageData();

                            // 渲染分页按钮
                            this.$nextTick(() => {
                                this.renderPageNumbers();
                            });
                        } else {
                            showMessage('获取样书失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.samples.loading = false;
                    }
                },

                // 处理排序
                handleSort(field) {
                    if (this.sortField === field) {
                        // 同一字段：升序 -> 降序 -> 取消排序
                        if (this.sortOrder === 'asc') {
                            this.sortOrder = 'desc';
                        } else if (this.sortOrder === 'desc') {
                            this.sortOrder = '';
                            this.sortField = '';
                        } else {
                            this.sortOrder = 'asc';
                        }
                    } else {
                        // 不同字段：直接设置为升序
                        this.sortField = field;
                        this.sortOrder = 'asc';
                    }

                    // 执行排序
                    this.applySorting();
                },

                // 应用排序
                applySorting() {
                    if (!this.sortField || !this.sortOrder) {
                        // 取消排序，恢复原始顺序
                        this.sortedSamples = [...this.samples.allSamples];
                    } else {
                        // 执行排序
                        this.sortedSamples = this.sortSamples([...this.samples.allSamples], this.sortField, this.sortOrder);
                    }

                    // 重新计算分页
                    this.samples.total = this.sortedSamples.length;
                    this.samples.totalPages = Math.ceil(this.samples.total / this.samples.pageSize);
                    this.samples.currentPage = 1;

                    // 更新当前页显示的数据
                    this.updateCurrentPageData();

                    // 重新渲染分页按钮
                    this.$nextTick(() => {
                        this.renderPageNumbers();
                    });
                },

                // 排序函数
                sortSamples(samples, field, order) {
                    return samples.sort((a, b) => {
                        let valueA, valueB;

                        switch (field) {
                            case 'name':
                                valueA = (a.name || '').toLowerCase();
                                valueB = (b.name || '').toLowerCase();
                                break;
                            case 'price':
                                valueA = parseFloat(a.price) || 0;
                                valueB = parseFloat(b.price) || 0;
                                break;
                            case 'publisher_name':
                                valueA = (a.publisher_name || '').toLowerCase();
                                valueB = (b.publisher_name || '').toLowerCase();
                                break;
                            case 'shipping_discount':
                                valueA = parseFloat(a.shipping_discount) || 0;
                                valueB = parseFloat(b.shipping_discount) || 0;
                                break;
                            case 'settlement_discount':
                                valueA = parseFloat(a.settlement_discount) || 0;
                                valueB = parseFloat(b.settlement_discount) || 0;
                                break;
                            case 'promotion_rate':
                                // 推广费率可能是用户填写的，也可能是系统计算的
                                valueA = parseFloat(a.promotion_rate) ||
                                        (a.shipping_discount && a.settlement_discount ?
                                         a.shipping_discount - a.settlement_discount : 0);
                                valueB = parseFloat(b.promotion_rate) ||
                                        (b.shipping_discount && b.settlement_discount ?
                                         b.shipping_discount - b.settlement_discount : 0);
                                break;
                            default:
                                return 0;
                        }

                        if (order === 'asc') {
                            return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
                        } else {
                            return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
                        }
                    });
                },

                // 更新当前页数据
                updateCurrentPageData() {
                    const startIndex = (this.samples.currentPage - 1) * this.samples.pageSize;
                    const endIndex = startIndex + this.samples.pageSize;

                    // 如果有排序，使用排序后的数据，否则使用原始数据
                    const sourceData = (this.sortField && this.sortOrder) ? this.sortedSamples : this.samples.allSamples;
                    this.samples.list = sourceData.slice(startIndex, endIndex);
                },

                // 统一的分页跳转方法
                goToPage(page) {
                    if (this.currentMode === 'filter') {
                        this.samples.currentPage = page;
                        this.applyAdvancedFilters();
                    } else if (this.sortField && this.sortOrder) {
                        // 排序状态下的分页
                        this.samples.currentPage = page;
                        this.updateCurrentPageData();
                    } else {
                        this.loadSamples(page);
                    }
                },

                // 搜索样书
                async searchSamples() {
                    await this.loadSamples(1);
                },

                // 查看样书详情
                async viewSampleDetail(sampleId) {
                    try {
                        const response = await $.ajax({
                            url: '/api/admin/get_admin_sample_detail',
                            type: 'GET',
                            data: { sample_id: sampleId }
                        });

                        if (response.code === 0) {
                            const sample = response.data;
                            this.showSampleDetailModal(sample);
                        } else {
                            showMessage('获取样书详情失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 显示样书详情模态框
                showSampleDetailModal(sample) {
                    const content = `
                        <div class="space-y-6">
                            <!-- 样书标题和基本信息 -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                                <div class="flex items-start gap-6 mb-4">
                                    <!-- 封面图片 -->
                                    <div class="flex-shrink-0">
                                        ${sample.attachment_link ? `
                                            <div class="relative group cursor-pointer" onclick="showCoverModal('${sample.attachment_link}', '${sample.name || '样书封面'}')">
                                                <img src="${sample.attachment_link}"
                                                     alt="${sample.name || '样书封面'}"
                                                     class="w-24 h-32 object-cover rounded-lg border-2 border-white shadow-lg hover:shadow-xl transition-shadow duration-300"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div style="display:none;" class="w-24 h-32 flex items-center justify-center bg-slate-100 rounded-lg border-2 border-white shadow-lg">
                                                    <div class="text-center text-slate-500">
                                                        <i class="fas fa-image text-xl mb-1"></i>
                                                        <div class="text-xs">封面加载失败</div>
                                                    </div>
                                                </div>
                                                <!-- 放大镜图标 -->
                                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-lg transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                    <i class="fas fa-search-plus text-white text-lg"></i>
                                                </div>
                                            </div>
                                        ` : `
                                            <div class="w-24 h-32 flex items-center justify-center bg-slate-200 rounded-lg border-2 border-white shadow-lg">
                                                <div class="text-center text-slate-400">
                                                    <i class="fas fa-book text-xl mb-1"></i>
                                                    <div class="text-xs">暂无封面</div>
                                                </div>
                                            </div>
                                        `}
                                    </div>

                                    <!-- 文字信息 -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-2xl font-bold text-slate-800 mb-2">${sample.name || '未填写'}</h3>
                                        <div class="space-y-2 text-sm text-slate-600 mb-4">
                                            <div class="flex items-center">
                                                <i class="fas fa-user text-slate-400 mr-2"></i>
                                                <span>作者：${sample.author || '未填写'}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-building text-slate-400 mr-2"></i>
                                                <span>版别：${sample.publisher_name || '未设置'}</span>
                                            </div>
                                        </div>

                                        <!-- 基础属性标签 -->
                                        <div class="space-y-3">
                                            <div class="flex flex-wrap gap-2">
                                                ${sample.level ? `
                                                    <span class="tag tag-level">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>${sample.level}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.book_type ? `
                                                    <span class="tag tag-book-type">
                                                        <i class="fas fa-book"></i>
                                                        <span>${sample.book_type}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.material_type ? `
                                                    <span class="tag tag-material">
                                                        <i class="fas fa-file-alt"></i>
                                                        <span>${sample.material_type}</span>
                                                    </span>
                                                ` : ''}
                                                ${sample.color_system ? `
                                                    <span class="tag ${sample.color_system === '彩色' ? 'tag-color-colorful' :
                                                                      sample.color_system === '双色' ? 'tag-color-dual' :
                                                                      sample.color_system === '四色' ? 'tag-color-four' : 'tag-default'}">
                                                        <i class="fas fa-palette"></i>
                                                        <span>${sample.color_system}</span>
                                                    </span>
                                                ` : ''}
                                            </div>

                                            <div class="flex flex-wrap gap-2">
                                                ${sample.national_regulation == 1 ? `
                                                    <span class="tag tag-national">
                                                        <i class="fas fa-star"></i>
                                                        <span>国家规划</span>
                                                        ${sample.national_regulation_level_name ?
                                                            `<span>(${sample.national_regulation_level_name})</span>` : ''}
                                                    </span>
                                                ` : ''}
                                                ${sample.provincial_regulation == 1 ? `
                                                    <span class="tag tag-provincial">
                                                        <i class="fas fa-medal"></i>
                                                        <span>省级规划</span>
                                                        ${sample.provincial_regulation_level_name ?
                                                            `<span>(${sample.provincial_regulation_level_name})</span>` : ''}
                                                    </span>
                                                ` : ''}
                                                ${sample.feature_name ? `
                                                    <span class="tag tag-feature">
                                                        <i class="fas fa-tags"></i>
                                                        <span>${sample.feature_name}</span>
                                                    </span>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 价格信息 -->
                                    <div class="text-right flex-shrink-0">
                                        <div class="text-3xl font-bold text-blue-600 mb-1">
                                            ${sample.price ? '¥' + parseFloat(sample.price).toFixed(2) : '未定价'}
                                        </div>
                                        <div class="text-sm text-slate-500 space-y-1">
                                            <div>ISBN: ${sample.isbn || '未填写'}</div>
                                            ${sample.publication_date ? `<div>出版时间: ${sample.publication_date}</div>` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 目录信息 -->
                            ${sample.directory_name ? `
                                <div class="bg-slate-50 rounded-xl p-4 border border-slate-200">
                                    <div class="flex items-center">
                                        <i class="fas fa-folder text-yellow-500 mr-3"></i>
                                        <div>
                                            <span class="text-sm font-medium text-slate-700">所属目录</span>
                                            <div class="text-slate-900 font-medium">${sample.directory_name}</div>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <!-- 折扣信息 -->
                            <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                    <i class="fas fa-percentage text-blue-500 mr-2"></i>
                                    折扣信息
                                </h4>
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="text-center p-4 bg-blue-50 rounded-xl border border-blue-100">
                                        <div class="text-xs text-blue-600 font-medium mb-2">发货折扣</div>
                                        <div class="text-lg font-bold text-blue-700">
                                            ${sample.shipping_discount ? (sample.shipping_discount * 100).toFixed(0) + '%' : '未设置'}
                                        </div>
                                    </div>
                                    <div class="text-center p-4 bg-green-50 rounded-xl border border-green-100">
                                        <div class="text-xs text-green-600 font-medium mb-2">结算折扣</div>
                                        <div class="text-lg font-bold text-green-700">
                                            ${sample.settlement_discount ? (sample.settlement_discount * 100).toFixed(0) + '%' : '未设置'}
                                        </div>
                                    </div>
                                    <div class="text-center p-4 bg-purple-50 rounded-xl border border-purple-100">
                                        <div class="text-xs text-purple-600 font-medium mb-2">推广费率</div>
                                        <div class="text-lg font-bold text-purple-700">
                                            ${sample.promotion_rate ?
                                                (sample.promotion_rate * 100).toFixed(0) + '%' :
                                                (sample.shipping_discount && sample.settlement_discount ?
                                                    ((sample.shipping_discount - sample.settlement_discount) * 100).toFixed(0) + '%' :
                                                    '未设置')}
                                        </div>
                                        <div class="text-xs text-purple-500 mt-1">
                                            ${sample.promotion_rate ? '用户填写' : '系统自动计算'}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 资源链接 -->
                            <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                                    <i class="fas fa-link text-green-500 mr-2"></i>
                                    资源链接
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-download text-blue-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">样书下载</div>
                                                    <div class="text-sm text-slate-500">PDF格式样书文件</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.sample_download_url ?
                                                    '<a href="' + sample.sample_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-book-open text-green-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">在线阅读</div>
                                                    <div class="text-sm text-slate-500">在线预览样书内容</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.online_reading_url ?
                                                    '<a href="' + sample.online_reading_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>阅读</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-folder-open text-orange-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">资源下载</div>
                                                    <div class="text-sm text-slate-500">教学辅助资源包</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.resource_download_url ?
                                                    '<a href="' + sample.resource_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-orange-500 text-white text-sm rounded-lg hover:bg-orange-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-presentation text-purple-500 mr-3"></i>
                                                <div>
                                                    <div class="font-medium text-slate-800">课件下载</div>
                                                    <div class="text-sm text-slate-500">PPT教学课件</div>
                                                </div>
                                            </div>
                                            <div>
                                                ${sample.courseware_download_url ?
                                                    '<a href="' + sample.courseware_download_url + '" target="_blank" class="inline-flex items-center px-3 py-2 bg-purple-500 text-white text-sm rounded-lg hover:bg-purple-600 transition-colors"><i class="fas fa-external-link-alt mr-1"></i>下载</a>' :
                                                    '<span class="text-sm text-slate-400">暂无资源</span>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 获奖信息 -->
                            ${sample.award_info ? `
                                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
                                    <h4 class="text-lg font-semibold text-slate-800 mb-3 flex items-center">
                                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                                        获奖信息
                                    </h4>
                                    <div class="bg-white rounded-lg p-4 border border-yellow-100">
                                        <p class="text-slate-700 leading-relaxed">${sample.award_info}</p>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `;

                    const footer = `
                        <div class="flex justify-end">
                            <button onclick="closeModal()"
                                    class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                关闭
                            </button>
                        </div>
                    `;

                    openModal('样书详情 - ' + sample.name, content, footer);
                },

                // 导出样书
                async exportSamples() {
                    if (!this.currentPublisherId) {
                        showMessage('请先选择出版社', 'warning');
                        return;
                    }

                    try {
                        showMessage('正在导出，请稍候...', 'info');

                        const params = new URLSearchParams();
                        params.append('publisher_id', this.currentPublisherId);

                        if (this.currentDirectoryId) {
                            params.append('directory_id', this.currentDirectoryId);
                        }
                        if (this.searchKeyword) {
                            params.append('search', this.searchKeyword);
                        }

                        window.open(`/api/admin/export_admin_samples?${params.toString()}`, '_blank');
                        showMessage('导出开始，请稍候下载...', 'success');
                    } catch (error) {
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                },

                // 编辑样书
                async editSample(sampleId) {
                    try {
                        const response = await $.ajax({
                            url: '/api/admin/get_admin_sample_detail',
                            type: 'GET',
                            data: { sample_id: sampleId }
                        });

                        if (response.code === 0) {
                            const sample = response.data;
                            this.showEditSampleModal(sample);
                        } else {
                            showMessage('获取样书详情失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 显示编辑样书模态框
                async showEditSampleModal(sample) {
                    try {
                        // 获取表单所需的数据
                        const [levelsRes, typesRes, nationalRes, provincialRes, featuresRes, colorsRes, materialsRes] = await Promise.all([
                            $.get('/api/admin/get_levels'),
                            $.get('/api/admin/book_types'),
                            $.get('/api/admin/get_national_regulation_levels'),
                            $.get('/api/admin/get_provincial_regulation_levels'),
                            $.get('/api/admin/get_features'),
                            $.get('/api/admin/color_systems'),
                            $.get('/api/admin/material_types')
                        ]);

                        const levels = levelsRes?.data || [];
                        const types = typesRes?.data || [];
                        const nationalLevels = nationalRes?.data || [];
                        const provincialLevels = provincialRes?.data || [];
                        const features = featuresRes?.data || [];
                        const colors = colorsRes?.data || [];
                        const materials = materialsRes?.data || [];

                        const content = `
                            <div class="space-y-6">
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                                        <span class="text-yellow-800 font-medium">管理员编辑模式</span>
                                    </div>
                                    <p class="text-yellow-700 text-sm mt-1">您正在以管理员身份编辑 "${sample.publisher_name}" 的样书</p>
                                </div>

                                <input type="hidden" id="sampleId" value="${sample.id}">

                                <!-- 基本信息 -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">样书名称 <span class="text-red-500">*</span></label>
                                        <input type="text" id="sampleName"
                                               value="${sample.name || ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入样书名称...">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">作者 <span class="text-red-500">*</span></label>
                                        <input type="text" id="sampleAuthor"
                                               value="${sample.author || ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入作者...">
                                    </div>
                                </div>

                                <!-- 封面上传 -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">样书封面</label>
                                    <div class="border-2 border-dashed border-slate-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors bg-slate-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-slate-400">
                                            <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
                                        </svg>
                                        <div>
                                            <label for="sampleCover" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                                <input id="sampleCover" type="file" accept=".jpg,.jpeg,.png,.webp" class="sr-only" />
                                                <i class="fas fa-upload mr-2"></i>${sample && sample.attachment_link ? '更换封面' : '选择封面'}
                                            </label>
                                            <span class="ml-2 text-slate-600 text-sm">或拖拽图片到此处</span>
                                        </div>
                                        <div class="file-info mt-2">
                                            <div class="text-sm text-slate-400">请选择JPG、PNG或WebP格式的图片（最大5MB）</div>
                                        </div>
                                        ${sample && sample.attachment_link ? `
                                            <div class="current-cover mt-4">
                                                <div class="text-sm text-slate-600 mb-2">当前封面：</div>
                                                <div class="flex justify-center">
                                                    <div class="relative">
                                                        <img src="${sample.attachment_link}"
                                                             alt="当前样书封面"
                                                             class="max-w-40 max-h-40 object-cover rounded-lg border border-slate-200 shadow-sm"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                        <div style="display:none;" class="max-w-40 max-h-40 flex items-center justify-center bg-slate-100 rounded-lg border border-slate-200">
                                                            <div class="text-center text-slate-500">
                                                                <i class="fas fa-image text-2xl mb-1"></i>
                                                                <div class="text-xs">封面加载失败</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">ISBN <span class="text-red-500">*</span></label>
                                        <input type="text" id="sampleIsbn"
                                               value="${sample.isbn || ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="请输入ISBN...">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">价格 <span class="text-red-500">*</span></label>
                                        <input type="number" id="samplePrice" step="0.01"
                                               value="${sample.price || ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入价格...">
                                    </div>
                                </div>

                                <!-- 出版时间 -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">出版时间 <span class="text-red-500">*</span></label>
                                    <input type="date" id="publicationDate"
                                           value="${sample.publication_date || ''}"
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="请选择出版时间"
                                           required>
                                </div>

                                <!-- 目录信息（管理员只读） -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">所属目录</label>
                                    <input type="text" id="sampleDirectory"
                                           value="${sample.directory_name || '未分类'}"
                                           readonly
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-600"
                                           placeholder="目录信息">
                                </div>

                                <!-- 版别信息（管理员只读） -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">版别</label>
                                    <input type="text" id="samplePublisher"
                                           value="${sample.publisher_name || ''}"
                                           readonly
                                           class="w-full px-4 py-3 border border-slate-300 rounded-xl bg-slate-50 text-slate-600"
                                           placeholder="版别信息">
                                </div>

                                <!-- 层次和类型 -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">学校层次</label>
                                        <select id="sampleLevel"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择层次</option>
                                            ${levels.map(level => {
                                                const selected = sample && sample.level === level.name ? 'selected' : '';
                                                return `<option value="${level.name}" ${selected}>${level.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">图书类型</label>
                                        <select id="sampleType"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择类型</option>
                                            ${types.map(type => {
                                                const selected = sample && sample.book_type === type.name ? 'selected' : '';
                                                return `<option value="${type.name}" ${selected}>${type.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                </div>

                                <!-- 特色（多选） -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-3">特色标签（可多选）</label>
                                    <div class="grid grid-cols-3 gap-3 max-h-32 overflow-y-auto custom-scrollbar border border-slate-200 rounded-xl p-4">
                                        ${features.map(feature => {
                                            // 检查当前样书是否有这个特色
                                            let checked = '';
                                            if (sample && sample.feature_name) {
                                                const sampleFeatures = sample.feature_name.split(',').map(f => f.trim());
                                                checked = sampleFeatures.includes(feature.name) ? 'checked' : '';
                                            }
                                            return `
                                                <label class="flex items-center space-x-2 cursor-pointer">
                                                    <input type="checkbox" name="feature_ids" value="${feature.id}" ${checked}
                                                           class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                    <span class="text-sm text-slate-700">${feature.name}</span>
                                                </label>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>

                                <!-- 折扣信息 -->
                                <div class="grid grid-cols-3 gap-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">发货折扣 <span class="text-red-500">*</span></label>
                                        <input type="number" id="shippingDiscount" step="0.0001" min="0" max="1"
                                               value="${sample.shipping_discount || '0.8'}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="0.8">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">结算折扣 <span class="text-red-500">*</span></label>
                                        <input type="number" id="settlementDiscount" step="0.0001" min="0" max="1"
                                               value="${sample.settlement_discount || '0.7'}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="0.7">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">推广费率</label>
                                        <input type="number" id="promotionRate" step="0.0001" min="0" max="1"
                                               value="${sample.promotion_rate || ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="自动计算">
                                    </div>
                                </div>

                                <!-- 规划信息 -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <label class="flex items-center space-x-2 mb-3">
                                            <input type="checkbox" id="nationalRegulation"
                                                   ${sample && sample.national_regulation ? 'checked' : ''}
                                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                            <span class="text-sm font-medium text-slate-700">国家规划教材</span>
                                        </label>
                                        <select id="nationalLevel"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                ${!sample || !sample.national_regulation ? 'disabled' : ''}>
                                            <option value="">请选择级别</option>
                                            ${nationalLevels.map(level => {
                                                const selected = sample && sample.national_regulation_level_id === level.id ? 'selected' : '';
                                                return `<option value="${level.id}" ${selected}>${level.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                    <div>
                                        <label class="flex items-center space-x-2 mb-3">
                                            <input type="checkbox" id="provincialRegulation"
                                                   ${sample && sample.provincial_regulation ? 'checked' : ''}
                                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                            <span class="text-sm font-medium text-slate-700">省级规划教材</span>
                                        </label>
                                        <select id="provincialLevel"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                ${!sample || !sample.provincial_regulation ? 'disabled' : ''}>
                                            <option value="">请选择级别</option>
                                            ${provincialLevels.map(level => {
                                                const selected = sample && sample.provincial_regulation_level_id === level.id ? 'selected' : '';
                                                return `<option value="${level.id}" ${selected}>${level.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                </div>

                                <!-- 材质和色系 -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">材质 <span class="text-red-500">*</span></label>
                                        <select id="materialType"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择材质</option>
                                            ${materials.map(material => {
                                                const selected = sample && sample.material_type === material.name ? 'selected' : '';
                                                return `<option value="${material.name}" ${selected}>${material.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">色系 <span class="text-red-500">*</span></label>
                                        <select id="colorSystem"
                                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择色系</option>
                                            ${colors.map(color => {
                                                const selected = sample && sample.color_system === color.name ? 'selected' : '';
                                                return `<option value="${color.name}" ${selected}>${color.name}</option>`;
                                            }).join('')}
                                        </select>
                                    </div>
                                </div>

                                <!-- 奖项信息 -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-slate-700 mb-2">获奖情况</label>
                                    <textarea id="sampleAwards" rows="2"
                                              class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                              placeholder="请输入获奖情况...">${sample ? sample.award_info || '' : ''}</textarea>
                                </div>

                                <!-- 课件和资源（按顺序排列） -->
                                <div class="space-y-6 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">课件描述</label>
                                        <textarea id="courseware" rows="3"
                                                  class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                  placeholder="请描述配套课件的类型和内容...">${sample ? sample.courseware || '' : ''}</textarea>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">课件下载链接</label>
                                        <input type="url" id="coursewareDownloadUrl"
                                               value="${sample ? sample.courseware_download_url || '' : ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入课件下载链接...">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">资源描述</label>
                                        <textarea id="resources" rows="3"
                                                  class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                  placeholder="请描述配套资源（如题库、教学大纲等）...">${sample ? sample.resources || '' : ''}</textarea>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">资源下载链接</label>
                                        <input type="url" id="resourceDownloadUrl"
                                               value="${sample ? sample.resource_download_url || '' : ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入资源下载链接...">
                                    </div>
                                </div>

                                <!-- 其他链接信息 -->
                                <div class="space-y-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">样书下载链接</label>
                                        <input type="url" id="sampleDownloadUrl"
                                               value="${sample ? sample.sample_download_url || '' : ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入样书下载链接...">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">在线试读链接</label>
                                        <input type="url" id="onlineReadingUrl"
                                               value="${sample ? sample.online_reading_url || '' : ''}"
                                               class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入在线试读链接...">
                                    </div>
                                </div>
                            </div>
                        `;

                        const footer = `
                            <div class="flex justify-end space-x-3">
                                <button onclick="closeModal()"
                                        class="px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors">
                                    取消
                                </button>
                                <button onclick="confirmEditSampleGlobal()"
                                        class="px-6 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
                                    <i class="fas fa-save mr-2"></i>
                                    保存修改
                                </button>
                            </div>
                        `;

                        openModal('编辑样书 - ' + sample.name, content, footer);

                        // 初始化规划复选框功能
                        this.initRegulationCheckboxes();

                        // 初始化文件上传功能
                        this.initFileUpload();

                    } catch (error) {
                        console.error('获取样书表单数据失败:', error);
                        showMessage('加载表单数据失败，请刷新页面重试', 'error');
                    }
                },

                // 确认编辑样书
                async confirmEditSample() {
                    const basicFormData = this.collectSampleFormData();
                    basicFormData.sample_id = document.getElementById('sampleId').value;

                    if (!this.validateSampleForm(basicFormData)) {
                        return;
                    }

                    // 创建FormData以支持文件上传
                    const formData = new FormData();

                    // 添加所有基本字段
                    Object.keys(basicFormData).forEach(key => {
                        formData.append(key, basicFormData[key]);
                    });

                    // 添加封面文件（如果选择了）
                    const coverFile = document.getElementById('sampleCover').files[0];
                    if (coverFile) {
                        formData.append('cover_file', coverFile);
                    }

                    try {
                        const response = await $.ajax({
                            url: '/api/admin/update_sample',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false
                        });

                        if (response.code === 0) {
                            showMessage('样书修改成功', 'success');
                            closeModal();
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            showMessage('修改失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                },

                // 收集样书表单数据
                collectSampleFormData() {
                    // 收集特色ID
                    const featureIds = [];
                    document.querySelectorAll('input[name="feature_ids"]:checked').forEach(checkbox => {
                        featureIds.push(parseInt(checkbox.value));
                    });

                    const formData = {
                        name: document.getElementById('sampleName').value.trim(),
                        author: document.getElementById('sampleAuthor').value.trim(),
                        isbn: document.getElementById('sampleIsbn').value.trim(),
                        price: document.getElementById('samplePrice').value,
                        publication_date: document.getElementById('publicationDate').value.trim(),
                        publisher_name: document.getElementById('samplePublisher').value,
                        level: document.getElementById('sampleLevel').value,
                        book_type: document.getElementById('sampleType').value,
                        shipping_discount: document.getElementById('shippingDiscount').value,
                        settlement_discount: document.getElementById('settlementDiscount').value,
                        promotion_rate: document.getElementById('promotionRate').value,
                        national_regulation: document.getElementById('nationalRegulation').checked ? '1' : '0',
                        national_regulation_level_id: document.getElementById('nationalLevel').value,
                        provincial_regulation: document.getElementById('provincialRegulation').checked ? '1' : '0',
                        provincial_regulation_level_id: document.getElementById('provincialLevel').value,
                        material_type: document.getElementById('materialType').value,
                        color_system: document.getElementById('colorSystem').value,
                        awards: document.getElementById('sampleAwards').value.trim(),
                        courseware: document.getElementById('courseware').value.trim(),
                        resources: document.getElementById('resources').value.trim(),
                        sample_download_url: document.getElementById('sampleDownloadUrl').value.trim(),
                        online_reading_url: document.getElementById('onlineReadingUrl').value.trim(),
                        resource_download_url: document.getElementById('resourceDownloadUrl').value.trim(),
                        courseware_download_url: document.getElementById('coursewareDownloadUrl').value.trim(),
                        // 将特色ID数组转换为JSON字符串
                        feature_ids: JSON.stringify(featureIds)
                    };

                    return formData;
                },

                // 验证样书表单
                validateSampleForm(formData) {
                    if (!formData.name) {
                        showMessage('请输入样书名称', 'error');
                        return false;
                    }
                    if (!formData.author) {
                        showMessage('请输入作者', 'error');
                        return false;
                    }
                    if (!formData.isbn) {
                        showMessage('请输入ISBN', 'error');
                        return false;
                    }
                    if (!formData.price || parseFloat(formData.price) <= 0) {
                        showMessage('请输入有效的价格', 'error');
                        return false;
                    }
                    if (!formData.publication_date) {
                        showMessage('请选择出版时间', 'error');
                        return false;
                    }
                    // 发货折扣验证（非必填，但如果填写则需要验证格式）
                    if (formData.shipping_discount && formData.shipping_discount.trim()) {
                        if (parseFloat(formData.shipping_discount) <= 0 || parseFloat(formData.shipping_discount) > 1) {
                            showMessage('发货折扣必须是0-1之间的数值', 'error');
                            return false;
                        }
                    }
                    // 结算折扣验证（非必填，但如果填写则需要验证格式）
                    if (formData.settlement_discount && formData.settlement_discount.trim()) {
                        if (parseFloat(formData.settlement_discount) <= 0 || parseFloat(formData.settlement_discount) > 1) {
                            showMessage('结算折扣必须是0-1之间的数值', 'error');
                            return false;
                        }
                    }
                    // 折扣关系验证（只在两个都有值时验证）
                    if (formData.shipping_discount && formData.shipping_discount.trim() &&
                        formData.settlement_discount && formData.settlement_discount.trim()) {
                        if (parseFloat(formData.shipping_discount) < parseFloat(formData.settlement_discount)) {
                            showMessage('发货折扣必须大于或等于结算折扣', 'error');
                            return false;
                        }
                    }
                    return true;
                },

                // 初始化规划复选框功能
                initRegulationCheckboxes() {
                    // 国家规划复选框
                    const nationalCheckbox = document.getElementById('nationalRegulation');
                    const nationalSelect = document.getElementById('nationalLevel');

                    if (nationalCheckbox && nationalSelect) {
                        nationalCheckbox.addEventListener('change', function() {
                            nationalSelect.disabled = !this.checked;
                            if (!this.checked) {
                                nationalSelect.value = '';
                            }
                        });
                    }

                    // 省级规划复选框
                    const provincialCheckbox = document.getElementById('provincialRegulation');
                    const provincialSelect = document.getElementById('provincialLevel');

                    if (provincialCheckbox && provincialSelect) {
                        provincialCheckbox.addEventListener('change', function() {
                            provincialSelect.disabled = !this.checked;
                            if (!this.checked) {
                                provincialSelect.value = '';
                            }
                        });
                    }
                },

                // 初始化文件上传功能
                initFileUpload() {
                    const fileInput = document.getElementById('sampleCover');
                    const fileInfo = document.querySelector('.file-info');

                    if (fileInput && fileInfo) {
                        fileInput.addEventListener('change', function(e) {
                            const file = e.target.files[0];
                            if (file) {
                                // 检查文件大小（5MB限制）
                                if (file.size > 5 * 1024 * 1024) {
                                    showMessage('文件大小不能超过5MB', 'error');
                                    this.value = '';
                                    return;
                                }

                                // 检查文件类型
                                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
                                if (!allowedTypes.includes(file.type)) {
                                    showMessage('请选择JPG、PNG或WebP格式的图片', 'error');
                                    this.value = '';
                                    return;
                                }

                                // 显示文件信息
                                fileInfo.innerHTML = `
                                    <div class="text-sm text-green-600">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)
                                    </div>
                                `;
                            } else {
                                fileInfo.innerHTML = '<div class="text-sm text-slate-400">请选择JPG、PNG或WebP格式的图片（最大5MB）</div>';
                            }
                        });
                    }
                },

                // 删除样书
                async deleteSample(sampleId, sampleName) {
                    if (!confirm(`确定要删除样书"${sampleName}"吗？此操作不可撤销！`)) {
                        return;
                    }

                    try {
                        const response = await $.ajax({
                            url: '/api/admin/delete_sample',
                            type: 'POST',
                            data: { sample_id: sampleId }
                        });

                        if (response.code === 0) {
                            showMessage('样书删除成功', 'success');
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                        } else {
                            showMessage('删除失败: ' + response.message, 'error');
                        }
                    } catch (error) {
                        showMessage('网络错误，请稍后重试', 'error');
                    }
                }
            }
        }

        // 处理自定义日期选择
        function handleCustomDateSelect() {
            showPublicationDateModal();
        }

        // 显示出版日期选择模态框
        function showPublicationDateModal() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('publicationStartDate').value = '';
            document.getElementById('publicationEndDate').value = today;
            document.getElementById('publicationDateModal').classList.remove('hidden');
        }

        // 关闭出版日期选择模态框
        function closePublicationDateModal() {
            document.getElementById('publicationDateModal').classList.add('hidden');
            // 如果没有设置自定义日期，重置选择
            const adminManagerInstance = window.adminManagerInstance;
            if (adminManagerInstance && (!adminManagerInstance.advancedFilters.customDateRange.start || !adminManagerInstance.advancedFilters.customDateRange.end)) {
                document.querySelector('input[name="publication_date_filter"][value=""]').checked = true;
                adminManagerInstance.advancedFilters.publicationDateFilter = '';
            }
        }

        // 确认出版日期范围
        function confirmPublicationDateRange() {
            const startDate = document.getElementById('publicationStartDate').value;
            const endDate = document.getElementById('publicationEndDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择开始日期和结束日期', 'warning');
                return;
            }

            if (startDate > endDate) {
                showMessage('开始日期不能晚于结束日期', 'warning');
                return;
            }

            // 保存自定义日期
            const adminManagerInstance = window.adminManagerInstance;
            if (adminManagerInstance) {
                adminManagerInstance.advancedFilters.customDateRange.start = startDate;
                adminManagerInstance.advancedFilters.customDateRange.end = endDate;
                adminManagerInstance.advancedFilters.publicationDateFilter = 'custom';

                // 关闭模态框
                closePublicationDateModal();

                // 应用筛选
                adminManagerInstance.applyAdvancedFilters();
            }
        }
    </script>

    <!-- 自定义出版日期选择模态框 -->
    <div id="publicationDateModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800">选择出版日期范围</h3>
                    <button onclick="closePublicationDateModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">开始日期</label>
                            <input type="date" id="publicationStartDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">结束日期</label>
                            <input type="date" id="publicationEndDate"
                                   class="w-full px-3 py-2 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>
                <!-- 模态框按钮 -->
                <div class="p-6 border-t border-slate-200 flex justify-end gap-3">
                    <button onclick="closePublicationDateModal()"
                            class="px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors">
                        取消
                    </button>
                    <button onclick="confirmPublicationDateRange()"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>
