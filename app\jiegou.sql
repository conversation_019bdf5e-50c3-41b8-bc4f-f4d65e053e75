/*
 Navicat Premium Dump SQL

 Source Server         : 火山云
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : **************:3306
 Source Schema         : managebooks

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 04/08/2025 23:30:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account_bindings
-- ----------------------------
DROP TABLE IF EXISTS `account_bindings`;
CREATE TABLE `account_bindings`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dealer_user_id` int NOT NULL COMMENT '经销商用户ID',
  `publisher_user_id` int NOT NULL COMMENT '出版社用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int NOT NULL COMMENT '创建者用户ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '绑定状态：1启用，0禁用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dealer_publisher`(`dealer_user_id` ASC, `publisher_user_id` ASC) USING BTREE,
  INDEX `idx_dealer_user_id`(`dealer_user_id` ASC) USING BTREE,
  INDEX `idx_publisher_user_id`(`publisher_user_id` ASC) USING BTREE,
  INDEX `fk_account_binding_creator`(`created_by` ASC) USING BTREE,
  CONSTRAINT `fk_account_binding_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_account_binding_dealer` FOREIGN KEY (`dealer_user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_account_binding_publisher` FOREIGN KEY (`publisher_user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '账号绑定表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int NULL DEFAULT NULL COMMENT '操作用户ID',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'success' COMMENT '操作结果：success成功，failure失败，partial部分成功',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作描述',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标对象类型（如：sample_book, user, order等）',
  `target_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标对象ID',
  `details` json NULL COMMENT '详细信息（JSON格式）',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户代理字符串',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_action_type`(`action_type` ASC) USING BTREE,
  INDEX `idx_result`(`result` ASC) USING BTREE,
  INDEX `idx_target`(`target_type` ASC, `target_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_user_action_time`(`user_id` ASC, `action_type` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 172 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for book_exhibitions
-- ----------------------------
DROP TABLE IF EXISTS `book_exhibitions`;
CREATE TABLE `book_exhibitions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `initiator_id` int NOT NULL COMMENT '发起人ID',
  `school_id` int NOT NULL COMMENT '学校ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动标题',
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '活动logo路径',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '书展介绍',
  `registration_deadline` datetime NOT NULL COMMENT '报名截止时间',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地点',
  `school_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学校地址',
  `requires_campus_registration` tinyint(1) NULL DEFAULT 0 COMMENT '是否需要进校报备',
  `registration_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报备要求',
  `registration_qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报备二维码图片路径',
  `allows_parking` tinyint(1) NULL DEFAULT 0 COMMENT '是否允许停车',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '其他要求',
  `status` enum('draft','pending_review','published','rejected','cancelled','ended') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'draft' COMMENT '活动状态：draft-草稿，pending_review-待审核，published-已发布，rejected-已拒绝，cancelled-已取消，ended-已结束',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `co_organizer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '协办方类型：publisher-出版社组织，dealer-经销商组织',
  `co_organizer_id` int NULL DEFAULT NULL COMMENT '协办方组织ID',
  `license_plate_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填车牌号码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_initiator_id`(`initiator_id` ASC) USING BTREE,
  INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
  INDEX `idx_co_organizer`(`co_organizer_type` ASC, `co_organizer_id` ASC) USING BTREE,
  CONSTRAINT `fk_exhibition_initiator` FOREIGN KEY (`initiator_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_exhibition_school` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '书展活动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for book_features
-- ----------------------------
DROP TABLE IF EXISTS `book_features`;
CREATE TABLE `book_features`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '特色名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特色描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for book_levels
-- ----------------------------
DROP TABLE IF EXISTS `book_levels`;
CREATE TABLE `book_levels`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for book_recommendations
-- ----------------------------
DROP TABLE IF EXISTS `book_recommendations`;
CREATE TABLE `book_recommendations`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `initiator_id` int NOT NULL COMMENT '发起人ID（经销商用户）',
  `initiator_company_id` int NOT NULL COMMENT '发起单位ID',
  `school_id` int NOT NULL COMMENT '学校ID',
  `school_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学校层次',
  `original_book_id` int NOT NULL COMMENT '原用教材ID',
  `original_book_supplier_id` int NOT NULL COMMENT '原用教材供应商ID',
  `replacement_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '换版原因',
  `replacement_reason_other` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '其他换版原因描述',
  `requirement_no_monopoly` tinyint(1) NOT NULL DEFAULT 0 COMMENT '禁用包销书',
  `requirement_recent_publish` tinyint(1) NOT NULL DEFAULT 0 COMMENT '近三年出版',
  `requirement_sufficient_stock` tinyint(1) NOT NULL DEFAULT 0 COMMENT '库存充足',
  `requirement_national_priority` tinyint(1) NOT NULL DEFAULT 0 COMMENT '国规优先',
  `requirement_other` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '其他要求',
  `recommendation_type` enum('direct','internal','external') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '推荐类型',
  `status` enum('in_progress','ended') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'in_progress' COMMENT '状态：推荐中、已结束',
  `referrer_id` int NULL DEFAULT NULL COMMENT '转荐人ID（内部推荐转外部推荐时的转荐人）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_initiator_id`(`initiator_id` ASC) USING BTREE,
  INDEX `idx_initiator_company_id`(`initiator_company_id` ASC) USING BTREE,
  INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
  INDEX `idx_original_book_id`(`original_book_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_recommendation_type`(`recommendation_type` ASC) USING BTREE,
  INDEX `fk_recomm_supplier`(`original_book_supplier_id` ASC) USING BTREE,
  INDEX `fk_recomm_referrer`(`referrer_id` ASC) USING BTREE,
  CONSTRAINT `fk_recomm_company` FOREIGN KEY (`initiator_company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_recomm_initiator` FOREIGN KEY (`initiator_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_recomm_original_book` FOREIGN KEY (`original_book_id`) REFERENCES `sample_books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_recomm_referrer` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_recomm_school` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_recomm_supplier` FOREIGN KEY (`original_book_supplier_id`) REFERENCES `publisher_companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '换版推荐主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for book_types
-- ----------------------------
DROP TABLE IF EXISTS `book_types`;
CREATE TABLE `book_types`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for color_systems
-- ----------------------------
DROP TABLE IF EXISTS `color_systems`;
CREATE TABLE `color_systems`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '色系名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '色系选项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for courseware_requests
-- ----------------------------
DROP TABLE IF EXISTS `courseware_requests`;
CREATE TABLE `courseware_requests`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `teacher_id` int NOT NULL COMMENT '申请老师ID',
  `sample_book_id` int NOT NULL COMMENT '样书ID',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '接收邮箱',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending' COMMENT '状态',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '拒绝原因',
  `delivery_method` enum('link','email') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发送方式',
  `resource_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源链接',
  `link_source` enum('official','third_party') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '链接来源',
  `download_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '下载说明',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher_id`(`teacher_id` ASC) USING BTREE,
  INDEX `idx_sample_book_id`(`sample_book_id` ASC) USING BTREE,
  CONSTRAINT `fk_courseware_req_book` FOREIGN KEY (`sample_book_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_courseware_req_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课件申请' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for custom_directories
-- ----------------------------
DROP TABLE IF EXISTS `custom_directories`;
CREATE TABLE `custom_directories`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目录名称',
  `school_id` int NULL DEFAULT NULL COMMENT '关联学校ID',
  `access_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '访问密钥',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建者用户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
  INDEX `idx_created_by`(`created_by` ASC) USING BTREE,
  CONSTRAINT `fk_custom_dir_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_custom_dir_school` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '个性化目录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for custom_directory_books
-- ----------------------------
DROP TABLE IF EXISTS `custom_directory_books`;
CREATE TABLE `custom_directory_books`  (
  `directory_id` int NOT NULL,
  `book_id` int NOT NULL,
  PRIMARY KEY (`directory_id`, `book_id`) USING BTREE,
  INDEX `fk_custom_dir_book_book`(`book_id` ASC) USING BTREE,
  CONSTRAINT `fk_custom_dir_book_book` FOREIGN KEY (`book_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_custom_dir_book_dir` FOREIGN KEY (`directory_id`) REFERENCES `custom_directories` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '个性化目录样书关联' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for custom_fields
-- ----------------------------
DROP TABLE IF EXISTS `custom_fields`;
CREATE TABLE `custom_fields`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `field_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字段类型，如：replacement_reason',
  `field_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字段值',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为系统字段（不可删除）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_field_type_value`(`field_type` ASC, `field_value` ASC) USING BTREE,
  INDEX `idx_field_type`(`field_type` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '自定义字段配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customers
-- ----------------------------
DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers`  (
  `customer_id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `contact_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`customer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dealer_addresses
-- ----------------------------
DROP TABLE IF EXISTS `dealer_addresses`;
CREATE TABLE `dealer_addresses`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dealer_company_id` int NOT NULL,
  `office_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '总部' COMMENT '办公地点名称',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `is_main` tinyint(1) NULL DEFAULT 0 COMMENT '是否为主要办公地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dealer_company_id`(`dealer_company_id` ASC) USING BTREE,
  INDEX `idx_is_main`(`is_main` ASC) USING BTREE,
  CONSTRAINT `dealer_addresses_ibfk_1` FOREIGN KEY (`dealer_company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '经销商地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dealer_companies
-- ----------------------------
DROP TABLE IF EXISTS `dealer_companies`;
CREATE TABLE `dealer_companies`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司简称',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `parent_company_id` int NULL DEFAULT NULL COMMENT '上级公司ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_dealer_company_parent`(`parent_company_id` ASC) USING BTREE,
  CONSTRAINT `fk_dealer_company_parent` FOREIGN KEY (`parent_company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dealer_company_permissions
-- ----------------------------
DROP TABLE IF EXISTS `dealer_company_permissions`;
CREATE TABLE `dealer_company_permissions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '经销商单位ID',
  `can_recommend_books` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有换版推荐权限',
  `can_invite_users` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有邀请注册权限',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `can_initiate_exhibition` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有发起书展权限',
  `can_register_exhibition` tinyint(1) NOT NULL COMMENT '是否有报名书展权限',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_company_id`(`company_id` ASC) USING BTREE,
  CONSTRAINT `fk_dealer_company_perm` FOREIGN KEY (`company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '经销商单位权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dealer_organizations
-- ----------------------------
DROP TABLE IF EXISTS `dealer_organizations`;
CREATE TABLE `dealer_organizations`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '组织名称',
  `parent_id` int NULL DEFAULT NULL COMMENT '上级组织ID',
  `company_id` int NOT NULL COMMENT '所属公司ID',
  `type` enum('company','department','branch') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'department' COMMENT '组织类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_company_id`(`company_id` ASC) USING BTREE,
  CONSTRAINT `fk_org_company` FOREIGN KEY (`company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_org_parent` FOREIGN KEY (`parent_id`) REFERENCES `dealer_organizations` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '经销商组织结构' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for dealers
-- ----------------------------
DROP TABLE IF EXISTS `dealers`;
CREATE TABLE `dealers`  (
  `user_id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `organization_id` int NULL DEFAULT NULL COMMENT '所属组织ID',
  `company_id` int NULL DEFAULT NULL COMMENT '所属公司ID',
  `customer_level` int NULL DEFAULT 3 COMMENT '客户级别，默认为3',
  `point_value` int NULL DEFAULT NULL COMMENT '加点值，为空时按规则自动计算',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `fk_dealer_organization`(`organization_id` ASC) USING BTREE,
  INDEX `fk_dealer_companie_id`(`company_id` ASC) USING BTREE,
  CONSTRAINT `fk_dealer_companie_id` FOREIGN KEY (`company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_dealer_organization` FOREIGN KEY (`organization_id`) REFERENCES `dealer_organizations` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for directories
-- ----------------------------
DROP TABLE IF EXISTS `directories`;
CREATE TABLE `directories`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parent_id` int NULL DEFAULT NULL,
  `publisher_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_parent_directory`(`parent_id` ASC) USING BTREE,
  INDEX `fk_publisher`(`publisher_id` ASC) USING BTREE,
  CONSTRAINT `fk_parent_directory` FOREIGN KEY (`parent_id`) REFERENCES `directories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_publisher` FOREIGN KEY (`publisher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for email_config
-- ----------------------------
DROP TABLE IF EXISTS `email_config`;
CREATE TABLE `email_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `smtp_host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` int NOT NULL DEFAULT 587 COMMENT 'SMTP端口',
  `smtp_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'SMTP用户名',
  `smtp_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'SMTP密码',
  `from_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发件人邮箱',
  `from_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发件人名称',
  `use_tls` tinyint(1) NULL DEFAULT 1 COMMENT '是否使用TLS',
  `use_ssl` tinyint(1) NULL DEFAULT 0 COMMENT '是否使用SSL',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '邮件配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exhibition_initiators
-- ----------------------------
DROP TABLE IF EXISTS `exhibition_initiators`;
CREATE TABLE `exhibition_initiators`  (
  `exhibition_id` int NOT NULL COMMENT '书展ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职务',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`exhibition_id`) USING BTREE,
  CONSTRAINT `fk_initiator_exhibition` FOREIGN KEY (`exhibition_id`) REFERENCES `book_exhibitions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发起人信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exhibition_participants
-- ----------------------------
DROP TABLE IF EXISTS `exhibition_participants`;
CREATE TABLE `exhibition_participants`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `registration_id` int NOT NULL COMMENT '报名ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色',
  `is_contact` tinyint(1) NULL DEFAULT 0 COMMENT '是否为联系人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `license_plate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '车牌号码',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_registration_id`(`registration_id` ASC) USING BTREE,
  CONSTRAINT `fk_participant_registration` FOREIGN KEY (`registration_id`) REFERENCES `exhibition_registrations` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参展人员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exhibition_permissions
-- ----------------------------
DROP TABLE IF EXISTS `exhibition_permissions`;
CREATE TABLE `exhibition_permissions`  (
  `publisher_id` int NOT NULL COMMENT '出版社/供应商ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`publisher_id`) USING BTREE,
  CONSTRAINT `fk_exhibition_perm_publisher` FOREIGN KEY (`publisher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '书展报名权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exhibition_registrations
-- ----------------------------
DROP TABLE IF EXISTS `exhibition_registrations`;
CREATE TABLE `exhibition_registrations`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `exhibition_id` int NOT NULL COMMENT '书展ID',
  `publisher_id` int NOT NULL COMMENT '出版社/供应商ID',
  `status` enum('registered','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'registered' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_exhibition_publisher`(`exhibition_id` ASC, `publisher_id` ASC) USING BTREE,
  INDEX `idx_exhibition_id`(`exhibition_id` ASC) USING BTREE,
  INDEX `idx_publisher_id`(`publisher_id` ASC) USING BTREE,
  CONSTRAINT `fk_exhibition_reg_exhibition` FOREIGN KEY (`exhibition_id`) REFERENCES `book_exhibitions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_exhibition_reg_publisher` FOREIGN KEY (`publisher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '书展报名' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for exhibition_review_history
-- ----------------------------
DROP TABLE IF EXISTS `exhibition_review_history`;
CREATE TABLE `exhibition_review_history`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `exhibition_id` int NOT NULL COMMENT '书展活动ID',
  `reviewer_id` int NOT NULL COMMENT '审核人ID',
  `reviewer_type` enum('publisher','dealer') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核人类型',
  `action` enum('submit','approve','reject','resubmit') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型：submit-提交审核，approve-审核通过，reject-审核拒绝，resubmit-重新提交',
  `old_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '新状态',
  `review_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exhibition_id`(`exhibition_id` ASC) USING BTREE,
  INDEX `idx_reviewer_id`(`reviewer_id` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `fk_review_exhibition` FOREIGN KEY (`exhibition_id`) REFERENCES `book_exhibitions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_reviewer` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '书展审核历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invitation_codes
-- ----------------------------
DROP TABLE IF EXISTS `invitation_codes`;
CREATE TABLE `invitation_codes`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邀请码',
  `inviter_id` int NOT NULL COMMENT '邀请人ID',
  `role` enum('teacher','dealer') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '被邀请人角色',
  `used` tinyint(1) NULL DEFAULT 0 COMMENT '是否已使用',
  `used_by` int NULL DEFAULT NULL COMMENT '使用人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expired_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE,
  INDEX `idx_inviter_id`(`inviter_id` ASC) USING BTREE,
  INDEX `idx_used_by`(`used_by` ASC) USING BTREE,
  CONSTRAINT `fk_invitation_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_invitation_used_by` FOREIGN KEY (`used_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '邀请码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for invitation_permissions
-- ----------------------------
DROP TABLE IF EXISTS `invitation_permissions`;
CREATE TABLE `invitation_permissions`  (
  `dealer_id` int NOT NULL COMMENT '经销商ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`dealer_id`) USING BTREE,
  CONSTRAINT `fk_invitation_perm_dealer` FOREIGN KEY (`dealer_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '邀请权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for list_visit_details
-- ----------------------------
DROP TABLE IF EXISTS `list_visit_details`;
CREATE TABLE `list_visit_details`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '详情记录ID',
  `visit_id` int NOT NULL COMMENT '访问记录ID',
  `book_id` int NULL DEFAULT NULL COMMENT '样书ID（如果是查看样书详情）',
  `action_type` enum('view_list','view_book','download_resource','copy_link') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型',
  `action_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行为时间',
  `additional_data` json NULL COMMENT '额外数据（JSON格式）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_visit_id`(`visit_id` ASC) USING BTREE,
  INDEX `idx_book_id`(`book_id` ASC) USING BTREE,
  INDEX `idx_action_type`(`action_type` ASC) USING BTREE,
  INDEX `idx_action_at`(`action_at` ASC) USING BTREE,
  INDEX `idx_book_action_time`(`book_id` ASC, `action_type` ASC, `action_at` ASC) USING BTREE,
  CONSTRAINT `fk_visit_detail_book` FOREIGN KEY (`book_id`) REFERENCES `sample_books` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_visit_detail_visit` FOREIGN KEY (`visit_id`) REFERENCES `list_visits` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '访问详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for list_visits
-- ----------------------------
DROP TABLE IF EXISTS `list_visits`;
CREATE TABLE `list_visits`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '访问记录ID',
  `list_id` int NOT NULL COMMENT '清单ID',
  `visitor_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '访问者IP地址',
  `visitor_fingerprint` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '访问者指纹',
  `user_id` int NULL DEFAULT NULL COMMENT '登录用户ID（如果是登录用户访问）',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户代理字符串',
  `visited_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_list_id`(`list_id` ASC) USING BTREE,
  INDEX `idx_visitor_fingerprint`(`visitor_fingerprint` ASC) USING BTREE,
  INDEX `idx_visited_at`(`visited_at` ASC) USING BTREE,
  INDEX `idx_list_visitor`(`list_id` ASC, `visitor_fingerprint` ASC) USING BTREE COMMENT '清单访问者复合索引',
  INDEX `idx_list_time_range`(`list_id` ASC, `visited_at` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_list_visit_list` FOREIGN KEY (`list_id`) REFERENCES `shared_lists` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_list_visit_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '清单访问记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for material_types
-- ----------------------------
DROP TABLE IF EXISTS `material_types`;
CREATE TABLE `material_types`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教材类型名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教材类型选项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for national_regulation_levels
-- ----------------------------
DROP TABLE IF EXISTS `national_regulation_levels`;
CREATE TABLE `national_regulation_levels`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规划名称，如十二五、十三五',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_items
-- ----------------------------
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `book_id` int NOT NULL COMMENT '样书ID',
  `school_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `shipped_quantity` int NOT NULL DEFAULT 0 COMMENT '发货量',
  `returned_quantity` int NOT NULL DEFAULT 0 COMMENT '退货量',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '单价',
  `promotion_report_id` int NULL DEFAULT NULL COMMENT '关联推广报备ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `from_dealer` tinyint NULL DEFAULT NULL COMMENT '是否来自经销商',
  `effective` tinyint NULL DEFAULT NULL COMMENT '是否有效',
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单编号',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '拒绝原因',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `reconciliation_status` enum('pre_settlement','pending_payment','settled') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pre_settlement' COMMENT '对账状态：预结算、待支付、已结算',
  `payment_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态：0未支付，1已支付',
  `publisher_quantity` int NULL DEFAULT NULL COMMENT '出版社方确认的数量',
  `dealer_quantity` int NULL DEFAULT NULL COMMENT '经销商方确认的数量',
  `publisher_confirm_status` enum('unconfirmed','confirmed','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'unconfirmed' COMMENT '出版社确认状态',
  `dealer_confirm_status` enum('unconfirmed','confirmed','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'unconfirmed' COMMENT '经销商确认状态',
  `matched_order_id` int NULL DEFAULT NULL COMMENT '匹配的对方订单ID',
  `last_modified_by` enum('publisher','dealer') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改方',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_book_id`(`book_id` ASC) USING BTREE,
  INDEX `idx_promotion_report_id`(`promotion_report_id` ASC) USING BTREE,
  INDEX `idx_order_number`(`order_number` ASC) USING BTREE,
  CONSTRAINT `fk_order_item_book` FOREIGN KEY (`book_id`) REFERENCES `sample_books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_order_item_promotion` FOREIGN KEY (`promotion_report_id`) REFERENCES `promotion_reports` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 77 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单明细' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_matches
-- ----------------------------
DROP TABLE IF EXISTS `order_matches`;
CREATE TABLE `order_matches`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `publisher_order_id` int NOT NULL COMMENT '出版社订单ID',
  `dealer_order_id` int NOT NULL COMMENT '经销商订单ID',
  `reconciliation_status` enum('pre_settlement','pending_payment','settled') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pre_settlement' COMMENT '对账状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_pair`(`publisher_order_id` ASC, `dealer_order_id` ASC) USING BTREE,
  INDEX `idx_publisher_order`(`publisher_order_id` ASC) USING BTREE,
  INDEX `idx_dealer_order`(`dealer_order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单匹配关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for order_reconciliation_history
-- ----------------------------
DROP TABLE IF EXISTS `order_reconciliation_history`;
CREATE TABLE `order_reconciliation_history`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL COMMENT '订单ID',
  `user_id` int NOT NULL COMMENT '操作用户ID',
  `user_role` enum('publisher','dealer') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户角色',
  `action_type` enum('upload','confirm','reject','modify','payment') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `old_quantity` int NULL DEFAULT NULL COMMENT '旧数量',
  `new_quantity` int NULL DEFAULT NULL COMMENT '新数量',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单对账历史记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for password_reset_codes
-- ----------------------------
DROP TABLE IF EXISTS `password_reset_codes`;
CREATE TABLE `password_reset_codes`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户邮箱',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '验证码',
  `user_id` int NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_reset_code_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '密码重置验证码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for promotion_reports
-- ----------------------------
DROP TABLE IF EXISTS `promotion_reports`;
CREATE TABLE `promotion_reports`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dealer_id` int NOT NULL,
  `sample_book_id` int NOT NULL,
  `school_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` enum('pending','approved','rejected','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending' COMMENT '状态',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `conflict_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `conflict_report_id` int NULL DEFAULT NULL,
  `expiry_date` date NULL DEFAULT NULL COMMENT '有效期',
  `promotion_status` enum('pending','in_progress','successful','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending' COMMENT '推广状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `promotion_reports_ibfk_1`(`dealer_id` ASC) USING BTREE,
  INDEX `fk_conflict_report`(`conflict_report_id` ASC) USING BTREE,
  CONSTRAINT `fk_conflict_report` FOREIGN KEY (`conflict_report_id`) REFERENCES `promotion_reports` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `promotion_reports_ibfk_1` FOREIGN KEY (`dealer_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 133 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for promotion_results
-- ----------------------------
DROP TABLE IF EXISTS `promotion_results`;
CREATE TABLE `promotion_results`  (
  `report_id` int NOT NULL COMMENT '报备ID',
  `order_item_id` int NULL DEFAULT NULL COMMENT '订单项ID',
  `is_successful` tinyint(1) NULL DEFAULT 0 COMMENT '是否成功',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`report_id`) USING BTREE,
  INDEX `idx_order_item_id`(`order_item_id` ASC) USING BTREE,
  CONSTRAINT `fk_prom_result_order_item` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_prom_result_report` FOREIGN KEY (`report_id`) REFERENCES `promotion_reports` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '报备结果' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for provincial_regulation_levels
-- ----------------------------
DROP TABLE IF EXISTS `provincial_regulation_levels`;
CREATE TABLE `provincial_regulation_levels`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规划名称，如省级十二五、省级十三五',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省份名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for publisher_addresses
-- ----------------------------
DROP TABLE IF EXISTS `publisher_addresses`;
CREATE TABLE `publisher_addresses`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `publisher_company_id` int NOT NULL,
  `office_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '总部' COMMENT '办公地点名称',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `is_main` tinyint(1) NULL DEFAULT 0 COMMENT '是否为主要办公地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_publisher_company_id`(`publisher_company_id` ASC) USING BTREE,
  INDEX `idx_is_main`(`is_main` ASC) USING BTREE,
  CONSTRAINT `publisher_addresses_ibfk_1` FOREIGN KEY (`publisher_company_id`) REFERENCES `publisher_companies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '出版社地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for publisher_companies
-- ----------------------------
DROP TABLE IF EXISTS `publisher_companies`;
CREATE TABLE `publisher_companies`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司简称',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_publisher` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否仅为出版社',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 896 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for publisher_company_permissions
-- ----------------------------
DROP TABLE IF EXISTS `publisher_company_permissions`;
CREATE TABLE `publisher_company_permissions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '出版社单位ID',
  `can_recommend_books` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有换版推荐权限',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `can_register_exhibition` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有书展报名权限',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_company_id`(`company_id` ASC) USING BTREE,
  CONSTRAINT `fk_publisher_company_perm` FOREIGN KEY (`company_id`) REFERENCES `publisher_companies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 896 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '出版社单位权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for recommendation_permissions
-- ----------------------------
DROP TABLE IF EXISTS `recommendation_permissions`;
CREATE TABLE `recommendation_permissions`  (
  `dealer_id` int NOT NULL COMMENT '经销商ID',
  `permission_type` enum('initiate','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`dealer_id`, `permission_type`) USING BTREE,
  CONSTRAINT `fk_recomm_perm_dealer` FOREIGN KEY (`dealer_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '推荐权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for recommendation_results
-- ----------------------------
DROP TABLE IF EXISTS `recommendation_results`;
CREATE TABLE `recommendation_results`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `recommendation_id` int NOT NULL COMMENT '推荐ID',
  `recommender_id` int NOT NULL COMMENT '推荐人ID',
  `recommended_book_id` int NOT NULL COMMENT '推荐样书ID',
  `stock_quantity` int NULL DEFAULT NULL COMMENT '库存数量',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'pending' COMMENT '推荐状态',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '推荐备注',
  `is_monopoly_conflict` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否包销冲突',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recommendation_id`(`recommendation_id` ASC) USING BTREE,
  INDEX `idx_recommender_id`(`recommender_id` ASC) USING BTREE,
  INDEX `idx_recommended_book_id`(`recommended_book_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_result_book` FOREIGN KEY (`recommended_book_id`) REFERENCES `sample_books` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_result_recommendation` FOREIGN KEY (`recommendation_id`) REFERENCES `book_recommendations` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_result_recommender` FOREIGN KEY (`recommender_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '推荐结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sample_book_features
-- ----------------------------
DROP TABLE IF EXISTS `sample_book_features`;
CREATE TABLE `sample_book_features`  (
  `sample_id` int NOT NULL COMMENT '样书ID',
  `feature_id` int NOT NULL COMMENT '特色ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`sample_id`, `feature_id`) USING BTREE,
  INDEX `idx_sample_id`(`sample_id` ASC) USING BTREE,
  INDEX `idx_feature_id`(`feature_id` ASC) USING BTREE,
  CONSTRAINT `fk_sample_feature_feature` FOREIGN KEY (`feature_id`) REFERENCES `book_features` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_sample_feature_sample` FOREIGN KEY (`sample_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '样书特色关联表（支持多选）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sample_book_price_changes
-- ----------------------------
DROP TABLE IF EXISTS `sample_book_price_changes`;
CREATE TABLE `sample_book_price_changes`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '变动记录ID',
  `sample_book_id` int NOT NULL COMMENT '样书ID',
  `change_type` enum('price','shipping_discount','settlement_discount','promotion_rate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变动类型：价格、发货折扣、结算折扣、推广费率',
  `old_value` decimal(10, 4) NULL DEFAULT NULL COMMENT '变动前的值',
  `new_value` decimal(10, 4) NOT NULL COMMENT '变动后的值',
  `change_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变动原因',
  `operator_id` int NOT NULL COMMENT '操作人ID',
  `operator_type` enum('publisher','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作人类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变动时间',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户代理',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sample_book_id`(`sample_book_id` ASC) USING BTREE,
  INDEX `idx_change_type`(`change_type` ASC) USING BTREE,
  INDEX `idx_operator_id`(`operator_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_sample_change_time`(`sample_book_id` ASC, `change_type` ASC, `created_at` DESC) USING BTREE,
  CONSTRAINT `fk_price_change_operator` FOREIGN KEY (`operator_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_price_change_sample` FOREIGN KEY (`sample_book_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '样书价格费率变动记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sample_books
-- ----------------------------
DROP TABLE IF EXISTS `sample_books`;
CREATE TABLE `sample_books`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `publisher_id` int NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `isbn` varchar(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `cooperation_status` tinyint(1) NULL DEFAULT 0,
  `publication_date` date NULL DEFAULT NULL COMMENT '出版时间',
  `award_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `attachment_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `parent_id` int NULL DEFAULT NULL,
  `discount_info` float NULL DEFAULT NULL,
  `dealer_discount` float NULL DEFAULT NULL,
  `level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层次',
  `book_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型',
  `national_regulation` tinyint(1) NULL DEFAULT 0 COMMENT '是否国规',
  `national_regulation_level_id` int NULL DEFAULT NULL COMMENT '国家规划级别ID',
  `provincial_regulation` tinyint(1) NULL DEFAULT 0 COMMENT '是否省规',
  `provincial_regulation_level_id` int NULL DEFAULT NULL COMMENT '省级规划级别ID',
  `publisher_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '版别',
  `courseware` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '课件描述',
  `courseware_download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '课件下载链接',
  `color_system` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '色系',
  `sample_download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样章下载链接',
  `resources` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源描述',
  `resource_download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源下载链接',
  `online_reading_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '在线试读链接',
  `material_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '教材类型',
  `shipping_discount` decimal(5, 4) NULL DEFAULT 1.0000 COMMENT '发货折扣，存储为小数形式，例如0.8表示8折',
  `settlement_discount` decimal(5, 4) NULL DEFAULT 1.0000 COMMENT '结算折扣，存储为小数形式，例如0.7表示7折',
  `promotion_rate` decimal(5, 4) NULL DEFAULT NULL COMMENT '推广费率，存储为小数形式，可选填，默认为发货折扣-结算折扣',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sample_books_ibfk_1`(`publisher_id` ASC) USING BTREE,
  INDEX `fk_directory`(`parent_id` ASC) USING BTREE,
  INDEX `fk_national_regulation_level`(`national_regulation_level_id` ASC) USING BTREE,
  INDEX `fk_provincial_regulation_level`(`provincial_regulation_level_id` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_book_type`(`book_type` ASC) USING BTREE,
  INDEX `idx_publisher_name`(`publisher_name` ASC) USING BTREE,
  INDEX `idx_national_regulation`(`national_regulation` ASC) USING BTREE,
  INDEX `idx_provincial_regulation`(`provincial_regulation` ASC) USING BTREE,
  INDEX `idx_national_regulation_level_id`(`national_regulation_level_id` ASC) USING BTREE,
  INDEX `idx_provincial_regulation_level_id`(`provincial_regulation_level_id` ASC) USING BTREE,
  CONSTRAINT `fk_directory` FOREIGN KEY (`parent_id`) REFERENCES `directories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_national_regulation_level` FOREIGN KEY (`national_regulation_level_id`) REFERENCES `national_regulation_levels` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_provincial_regulation_level` FOREIGN KEY (`provincial_regulation_level_id`) REFERENCES `provincial_regulation_levels` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `sample_books_ibfk_1` FOREIGN KEY (`publisher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `check_discount_relation` CHECK (`shipping_discount` >= `settlement_discount`)
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sample_requests
-- ----------------------------
DROP TABLE IF EXISTS `sample_requests`;
CREATE TABLE `sample_requests`  (
  `request_id` int NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批量申请的订单编号',
  `teacher_id` int NULL DEFAULT NULL,
  `textbook_id` int NULL DEFAULT NULL,
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `request_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请日期',
  `approval_date` datetime NULL DEFAULT NULL COMMENT '处理日期',
  `shipping_date` datetime NULL DEFAULT NULL COMMENT '发货日期',
  `address_id` int NULL DEFAULT NULL,
  `tracking_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `shipping_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `request_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '申请说明',
  `quantity` int NULL DEFAULT 1 COMMENT '申请数量',
  `purpose` enum('教材','教参') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '教材' COMMENT '用途',
  `course_id` int NULL DEFAULT NULL COMMENT '关联的主讲课程ID',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  PRIMARY KEY (`request_id`) USING BTREE,
  INDEX `teacher_id`(`teacher_id` ASC) USING BTREE,
  INDEX `address_id_idx`(`address_id` ASC) USING BTREE,
  INDEX `sample_requests_ibfk_2`(`textbook_id` ASC) USING BTREE,
  INDEX `fk_sample_request_course`(`course_id` ASC) USING BTREE,
  INDEX `idx_order_number`(`order_number` ASC) USING BTREE,
  CONSTRAINT `fk_sample_request_address` FOREIGN KEY (`address_id`) REFERENCES `shipping_addresses` (`address_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_sample_request_course` FOREIGN KEY (`course_id`) REFERENCES `teacher_courses` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `sample_requests_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `sample_requests_ibfk_2` FOREIGN KEY (`textbook_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 138 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for school_addresses
-- ----------------------------
DROP TABLE IF EXISTS `school_addresses`;
CREATE TABLE `school_addresses`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `school_id` int NOT NULL,
  `campus_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '主校区' COMMENT '校区名称',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `is_main` tinyint(1) NULL DEFAULT 0 COMMENT '是否为主校区地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_school_id`(`school_id` ASC) USING BTREE,
  INDEX `idx_is_main`(`is_main` ASC) USING BTREE,
  CONSTRAINT `school_addresses_ibfk_1` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学校地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for schools
-- ----------------------------
DROP TABLE IF EXISTS `schools`;
CREATE TABLE `schools`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学校简称',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市',
  `school_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '办学层次',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2965 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shared_list_books
-- ----------------------------
DROP TABLE IF EXISTS `shared_list_books`;
CREATE TABLE `shared_list_books`  (
  `list_id` int NOT NULL COMMENT '清单ID',
  `book_id` int NOT NULL COMMENT '样书ID',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `added_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`list_id`, `book_id`) USING BTREE,
  INDEX `idx_book_id`(`book_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`list_id` ASC, `sort_order` ASC) USING BTREE,
  CONSTRAINT `fk_shared_list_book_book` FOREIGN KEY (`book_id`) REFERENCES `sample_books` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_shared_list_book_list` FOREIGN KEY (`list_id`) REFERENCES `shared_lists` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '清单样书关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shared_lists
-- ----------------------------
DROP TABLE IF EXISTS `shared_lists`;
CREATE TABLE `shared_lists`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '清单ID',
  `creator_id` int NOT NULL COMMENT '创建者用户ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '清单标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '清单描述',
  `share_token` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分享令牌(UUID)',
  `access_level` enum('public','private','password_protected','login_required') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'public' COMMENT '访问级别',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码哈希值',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_share_token`(`share_token` ASC) USING BTREE COMMENT '分享令牌唯一索引',
  INDEX `idx_creator_id`(`creator_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_access_level`(`access_level` ASC) USING BTREE,
  INDEX `idx_share_token_active`(`share_token` ASC, `is_active` ASC) USING BTREE,
  CONSTRAINT `fk_shared_list_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分享清单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shipping_addresses
-- ----------------------------
DROP TABLE IF EXISTS `shipping_addresses`;
CREATE TABLE `shipping_addresses`  (
  `address_id` int NOT NULL AUTO_INCREMENT,
  `teacher_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区县',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详细地址',
  PRIMARY KEY (`address_id`) USING BTREE,
  INDEX `teacher_id_idx`(`teacher_id` ASC) USING BTREE,
  CONSTRAINT `fk_teacher_address` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for shipping_addresses_backup
-- ----------------------------
DROP TABLE IF EXISTS `shipping_addresses_backup`;
CREATE TABLE `shipping_addresses_backup`  (
  `address_id` int NOT NULL DEFAULT 0,
  `teacher_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for site_settings
-- ----------------------------
DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_role` enum('teacher','publisher','dealer','admin','default') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户角色，default为默认配置',
  `site_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '网站名称',
  `login_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '登录入口URL',
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '网站logo文件路径',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_role`(`user_role` ASC) USING BTREE COMMENT '每个角色只能有一个配置'
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '网站设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teacher_courses
-- ----------------------------
DROP TABLE IF EXISTS `teacher_courses`;
CREATE TABLE `teacher_courses`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `teacher_id` int NOT NULL COMMENT '教师ID',
  `course_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主讲课程名称',
  `semester` enum('春季','秋季') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '开课季',
  `course_type` enum('公共课','专业课') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程性质',
  `student_count` int NOT NULL COMMENT '开课人数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher_id`(`teacher_id` ASC) USING BTREE,
  CONSTRAINT `fk_teacher_course` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教师主讲课程' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teacher_info
-- ----------------------------
DROP TABLE IF EXISTS `teacher_info`;
CREATE TABLE `teacher_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `teacher_id` int NOT NULL COMMENT '教师用户ID',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '院系',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职务',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_teacher_id`(`teacher_id` ASC) USING BTREE COMMENT '每个教师只能有一条记录',
  CONSTRAINT `fk_teacher_info_user` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教师详细信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `user_permissions`;
CREATE TABLE `user_permissions`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '权限记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `permission_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `permission_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'deny' COMMENT '权限值：allow允许，deny禁止',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int NULL DEFAULT NULL COMMENT '创建者用户ID',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_permission`(`user_id` ASC, `permission_name` ASC) USING BTREE COMMENT '用户权限唯一索引',
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_permission_name`(`permission_name` ASC) USING BTREE,
  INDEX `idx_created_by`(`created_by` ASC) USING BTREE,
  CONSTRAINT `fk_user_permission_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_permission_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_registrations
-- ----------------------------
DROP TABLE IF EXISTS `user_registrations`;
CREATE TABLE `user_registrations`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int NOT NULL COMMENT '注册用户ID',
  `source_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '注册来源类型',
  `source_id` int NULL DEFAULT NULL COMMENT '来源ID（如清单ID）',
  `source_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '来源标识（如分享token）',
  `source_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '来源URL',
  `registered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `idx_source_id`(`source_id` ASC) USING BTREE,
  INDEX `idx_source_token`(`source_token` ASC) USING BTREE,
  INDEX `idx_registered_at`(`registered_at` ASC) USING BTREE,
  CONSTRAINT `fk_user_registration_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户注册来源统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for username_change_history
-- ----------------------------
DROP TABLE IF EXISTS `username_change_history`;
CREATE TABLE `username_change_history`  (
  `user_id` int NOT NULL COMMENT '用户ID',
  `old_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原用户名',
  `new_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '新用户名',
  `change_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  CONSTRAINT `fk_username_change_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户名修改时间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `user_id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role` enum('teacher','publisher','dealer','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `contact_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone_number` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `teacher_school_id` int NULL DEFAULT NULL,
  `publisher_company_id` int NULL DEFAULT NULL,
  `dealer_company_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `username_unique`(`username` ASC) USING BTREE,
  UNIQUE INDEX `phone_number_unique`(`phone_number` ASC) USING BTREE,
  INDEX `teacher_school_id`(`teacher_school_id` ASC) USING BTREE,
  INDEX `publisher_company_id`(`publisher_company_id` ASC) USING BTREE,
  INDEX `dealer_company_id`(`dealer_company_id` ASC) USING BTREE,
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`teacher_school_id`) REFERENCES `schools` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`publisher_company_id`) REFERENCES `publisher_companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `users_ibfk_3` FOREIGN KEY (`dealer_company_id`) REFERENCES `dealer_companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
